# MCP Financial Analyzer - Orchestration System

This document describes the comprehensive orchestration system that coordinates the three specialized financial analysis agents: MySQL Agent, Storage Analyzer Agent, and Alert Manager Agent.

## Architecture Overview

The orchestration system consists of several key components that work together to provide intelligent workflow management:

### Core Components

1. **Financial Orchestrator** (`orchestrator/financial_orchestrator.py`)
   - Main orchestrator class extending the MCP framework
   - Implements query-specific workflow routing
   - Manages agent coordination and context sharing

2. **Context Manager** (`orchestrator/context_manager.py`)
   - Advanced context management and data persistence
   - Handles data transformation between agents
   - Provides workflow state management and recovery

3. **Query Processor** (`orchestrator/query_processor.py`)
   - Intelligent natural language query parsing
   - Entity and parameter extraction
   - Query type classification and routing decisions

4. **Workflow Patterns** (`orchestrator/workflow_patterns.py`)
   - Predefined workflow templates for common scenarios
   - Support for sequential, parallel, and conditional execution
   - Extensible pattern registry system

5. **Orchestration Runner** (`orchestrator/orchestration_runner.py`)
   - Main execution engine and public API
   - Integration point for all orchestration components
   - Performance monitoring and statistics

6. **Agent Interfaces** (`agents/agent_interfaces.py`)
   - Standardized interfaces for existing agents
   - Context-aware method wrappers
   - Error handling and retry logic

## Supported Workflow Patterns

### 1. Shortage Analysis Workflow
**Pattern**: MySQL → Shortage → Alert  
**Use Case**: Inventory shortage analysis with notifications  
**Query Examples**:
- "Analyze shortage for order CUSTORD-*********"
- "Check inventory risk for MM2004 80GB GPUs"

### 2. Supplier Risk Assessment
**Pattern**: MySQL → Shortage  
**Use Case**: Vendor reliability and delivery risk analysis  
**Query Examples**:
- "Assess supplier risk for MetaMind Technology"
- "Analyze delivery performance for DEP9005 CPUs"

### 3. Customer Priority Management
**Pattern**: MySQL → Alert  
**Use Case**: Customer order prioritization and notifications  
**Query Examples**:
- "Review customer priorities for Tech Pioneer Co"
- "Check delivery conflicts between customers"

### 4. Comprehensive Analysis
**Pattern**: MySQL → Shortage → Alert  
**Use Case**: Full-spectrum analysis for complex scenarios  
**Query Examples**:
- "Perform end-to-end supply chain stress test"
- "Analyze all active work orders and dependencies"

## Usage Examples

### Basic Usage

```python
from orchestrator import create_orchestration_runner

# Create orchestration runner
runner = create_orchestration_runner(
    mysql_agent=mysql_agent,
    shortage_agent=shortage_agent,
    alert_agent=alert_agent,
    llm_factory=llm_factory
)

# Execute financial analysis query
result = await runner.execute_financial_query(
    query="Analyze shortage for order CUSTORD-*********",
    execution_mode="pattern_based"
)

# Process results
if result["success"]:
    print(f"Query Type: {result['query_analysis']['type']}")
    print(f"Workflow: {result['query_analysis']['workflow_pattern']}")
    print(f"Execution Time: {result['execution_time']:.2f}s")
else:
    print(f"Error: {result['error']}")
```

### Advanced Usage with Custom Parameters

```python
# Execute with custom parameters
result = await runner.execute_financial_query(
    query="Analyze multi-customer priority conflict",
    execution_mode="orchestrator_based",
    workflow_id="custom_workflow_001",
    context_dir="/path/to/contexts",
    persist_context=True
)

# Get execution statistics
stats = runner.get_execution_statistics()
print(f"Total workflows: {stats['total_workflows']}")
print(f"Success rate: {stats['successful_workflows'] / stats['total_workflows'] * 100:.1f}%")
```

## Running the System

### 1. Prerequisites
Ensure all required MCP servers are running:

```bash
# Start shortage-index server (port 6970)
cd /merge/agent_develop && python -m index.server --host 0.0.0.0 --port 6970

# Start alert-notification server (port 6972)  
cd /merge/agent_develop && python -m notification.server --host 0.0.0.0 --port 6972

# Start MySQL database server (port 8702)
cd /merge/dev_agent/db_agent_develop && python -m MySQL.server --host 0.0.0.0 --port 8702
```

### 2. Run Orchestrated Analysis

```bash
# Run demo scenarios
python orchestrated_main.py demo

# Run interactive mode
python orchestrated_main.py interactive

# Run health check only
python orchestrated_main.py health
```

### 3. Run Integration Tests

```bash
# Run orchestration system tests
python test_orchestration.py
```

## Query Processing Features

### Entity Extraction
The system automatically extracts relevant entities from queries:

- **Order Numbers**: CUSTORD-YYYYMMXXX, WO-YYYYMMXXX
- **Material Codes**: MM2004, HCS500, DEP9005, etc.
- **Supplier Names**: MetaMind Technology, AVATA Technology
- **Customer Names**: Tech Pioneer Co, QCT Technology
- **Product Models**: G7B Golden_1, G8D LGD_2

### Parameter Extraction
- **Quantities**: Numbers with units (1000 units, 150 pieces)
- **Dates**: Various formats (YYYY-MM-DD, MM/DD/YYYY)
- **Priorities**: High, medium, low priority indicators
- **Percentages**: Risk thresholds and performance metrics

### Query Classification
Automatic classification into query types:

- `shortage_analysis`: Inventory and shortage queries
- `supplier_risk`: Vendor reliability assessment
- `customer_priority`: Order prioritization and conflicts
- `comprehensive`: Complex multi-dimensional analysis

## Context Management

### Context Data Structure
The system maintains hierarchical context throughout workflow execution:

```python
FinancialWorkflowContext:
  ├── workflow_id: "workflow_001"
  ├── original_query: "User's natural language query"
  ├── query_type: "shortage_analysis"
  ├── mysql_context: MySQLContextData
  ├── shortage_context: ShortageContextData
  ├── alert_context: AlertContextData
  └── metadata: execution tracking and statistics
```

### Context Sharing
Each agent receives relevant context from previous agents:

- **MySQL Agent**: Receives original query and parameters
- **Shortage Agent**: Receives MySQL results and historical data
- **Alert Agent**: Receives both MySQL and shortage analysis results

### Context Persistence
Workflow contexts can be persisted to disk for:
- Debugging and analysis
- Workflow resumption after failures
- Performance optimization insights

## Error Handling and Recovery

### Agent-Level Recovery
- Automatic retry with exponential backoff
- Graceful degradation when optional steps fail
- Comprehensive error logging and reporting

### Workflow-Level Recovery
- Continue execution when non-critical agents fail
- Rollback to last successful checkpoint
- Clear error messages with recovery suggestions

### Query Clarification
For ambiguous queries, the system provides:
- Ambiguity detection and flagging
- Suggested clarifications for users
- Confidence scoring for classification decisions

## Performance Features

### Execution Modes
- **Pattern-Based**: Uses predefined workflow patterns (faster)
- **Orchestrator-Based**: Uses dynamic orchestration (more flexible)

### Monitoring and Statistics
- Execution time tracking
- Success/failure rate monitoring
- Agent performance metrics
- Workflow complexity analysis

### Optimization Features
- Query caching for repeated patterns
- Context reuse for similar workflows
- Parallel execution where possible

## Configuration

### MCP Server Configuration
Ensure your `mcp_agent.config.yaml` includes:

```yaml
mcp:
  servers:
    mysql:
      transport: sse
      url: http://localhost:8702/sse
    shortage-index:
      transport: sse  
      url: http://localhost:6970/sse
    alert-notification:
      transport: sse
      url: http://localhost:6972/sse
```

### Agent Configuration
Agents are configured with orchestration-aware settings:

```python
# Example agent initialization with orchestration support
mysql_agent = create_mysql_orchestrator_agent()
shortage_agent = create_shortage_analyzer_agent("CompanyName")
alert_agent = create_alert_manager_agent("CompanyName")
```

## Integration with Existing Tests

The orchestration system maintains compatibility with existing test scenarios:

### Test Scenarios Supported
All 13 original test scenarios from `test_workflow_scenarios.py`:

1. **Mass Production GPU Shortage Crisis**
2. **Supplier Reliability Crisis**
3. **Multi-Customer Resource Conflict**
4. **Critical RAM Shortage Analysis**
5. **Power Supply Bottleneck Crisis**
6. **Storage Component Cross-Product Conflict**
7. **Cascading Component Shortage Analysis**
8. **Supplier Consolidation Risk Assessment**
9. **Factory Capacity vs Material Availability Mismatch**
10. **Enhanced Multi-Customer Priority Conflict Analysis**
11. **Purchase Request Approval Backlog Impact Analysis**
12. **Work Order Material Issuance Gap Analysis**
13. **End-to-End Supply Chain Stress Test**

### Validation
Run the original scenarios through the orchestration system:

```python
from test_workflow_scenarios import WorkflowTestScenarios

# Execute through orchestrator
result = await runner.execute_financial_query(
    query="Original test scenario query",
    execution_mode="pattern_based"
)
```

## Future Enhancements

### Planned Features
1. **Machine Learning Integration**: Query classification improvement
2. **Advanced Parallel Execution**: Dependency-based scheduling
3. **Real-time Monitoring Dashboard**: Workflow visualization
4. **Custom Workflow Designer**: Visual workflow creation
5. **Integration APIs**: REST/GraphQL endpoints

### Extensibility
The orchestration system is designed for easy extension:

- **New Query Types**: Add to `QueryType` enum and keyword mappings
- **Custom Workflows**: Register new patterns in `WorkflowPatternRegistry`
- **Additional Agents**: Implement `OrchestratableAgent` protocol
- **Custom Context**: Extend context data structures

## Troubleshooting

### Common Issues

1. **MCP Servers Not Available**
   - Check server status with health check endpoints
   - Verify port configurations match expectations
   - Ensure servers are started in correct order

2. **Query Classification Issues**
   - Check confidence scores in results
   - Review entity extraction results
   - Add keywords to improve classification

3. **Context Sharing Problems**
   - Verify agent interfaces are properly initialized
   - Check context persistence settings
   - Review error logs for validation failures

4. **Performance Issues**
   - Monitor execution statistics
   - Check for resource bottlenecks
   - Optimize query patterns for better routing

### Debug Mode
Enable detailed logging:

```python
import logging
logging.getLogger("orchestrator").setLevel(logging.DEBUG)
```

### Health Checks
Run comprehensive health check:

```python
health = await runner.health_check()
print(f"System status: {health['status']}")
```

## Contributing

When contributing to the orchestration system:

1. **Follow Existing Patterns**: Use established architectural patterns
2. **Add Tests**: Include unit tests for new components
3. **Update Documentation**: Document new features and changes
4. **Maintain Compatibility**: Ensure backward compatibility with existing agents
5. **Performance Considerations**: Monitor impact on execution time

## Support

For questions and support:
- Review logs in debug mode
- Check health status of all components  
- Consult existing test scenarios for examples
- Verify MCP server configurations

The orchestration system provides a robust, scalable foundation for coordinating complex financial analysis workflows while maintaining the flexibility to adapt to new requirements and use cases.