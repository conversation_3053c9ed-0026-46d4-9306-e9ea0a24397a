"""
Agent Interfaces for Orchestrated Financial Analysis
===================================================

Standardized interfaces and adapters for integrating existing agents with
the orchestration system. This module provides unified APIs and ensures
consistent data flow between agents.

Key Components:
- Base agent interface definitions
- Context-aware method wrappers
- Data transformation utilities  
- Error handling and retry logic
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Protocol, runtime_checkable
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime

from pydantic import BaseModel, Field, ValidationError

from mcp_agent.logging.logger import get_logger

logger = get_logger(__name__)


@dataclass
class AgentExecutionContext:
    """Context data passed to agents during orchestrated execution."""
    workflow_id: str
    step_id: str
    agent_name: str
    original_query: str
    shared_context: str = ""
    previous_results: Dict[str, Any] = None
    execution_metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.previous_results is None:
            self.previous_results = {}
        if self.execution_metadata is None:
            self.execution_metadata = {}


class AgentInputSchema(BaseModel):
    """Base input schema for orchestrated agent execution."""
    query: str = Field(description="Primary query or instruction")
    context: AgentExecutionContext = Field(description="Execution context")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Additional parameters")
    
    class Config:
        arbitrary_types_allowed = True


class AgentOutputSchema(BaseModel):
    """Base output schema for orchestrated agent execution."""
    success: bool = Field(description="Whether execution was successful")
    result: Any = Field(description="Primary result data")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Execution metadata")
    error: Optional[str] = Field(default=None, description="Error message if failed")
    execution_time: float = Field(default=0.0, description="Execution time in seconds")
    warnings: List[str] = Field(default_factory=list, description="Non-fatal warnings")


@runtime_checkable
class OrchestratableAgent(Protocol):
    """Protocol defining the interface for orchestratable agents."""
    
    async def execute_orchestrated(
        self,
        input_data: AgentInputSchema
    ) -> AgentOutputSchema:
        """Execute agent with orchestration context."""
        ...
    
    async def initialize_for_orchestration(self) -> bool:
        """Initialize agent for orchestrated execution."""
        ...
    
    def get_agent_capabilities(self) -> Dict[str, Any]:
        """Get agent capabilities and configuration."""
        ...


class MySQLAgentInterface:
    """Interface adapter for MySQL agent orchestration."""
    
    def __init__(self, mysql_agent: Any):
        """Initialize with existing MySQL agent instance."""
        self.agent = mysql_agent
        self.agent_name = "mysql_analyzer"
        self._initialized = False
        logger.info(f"MySQLAgentInterface created for {self.agent_name}")
    
    async def initialize_for_orchestration(self) -> bool:
        """Initialize MySQL agent for orchestrated execution."""
        try:
            if hasattr(self.agent, 'initialize_llm') and not self._initialized:
                await self.agent.initialize_llm()
                self._initialized = True
                logger.info("MySQL agent initialized for orchestration")
            return True
        except Exception as e:
            logger.error(f"MySQL agent initialization failed: {e}")
            return False
    
    async def execute_orchestrated(self, input_data: AgentInputSchema) -> AgentOutputSchema:
        """Execute MySQL analysis with orchestration context."""
        start_time = asyncio.get_event_loop().time()
        
        try:
            if not self._initialized:
                await self.initialize_for_orchestration()
            
            # Import required components
            from agents.mysql_agent import (
                MCPOrchestratorInputSchema,
                safe_orchestrator_run,
                tool_schema_to_class_map,
                FinalResponseSchema
            )
            
            # Prepare MySQL-specific input
            query = input_data.query
            context = input_data.context
            
            # Enhance query with context if available
            if context.shared_context:
                enhanced_query = f"{query}\n\nContext: {context.shared_context}"
            else:
                enhanced_query = query
            
            mysql_input = MCPOrchestratorInputSchema(query=enhanced_query)
            
            # Execute MySQL agent workflow
            mysql_output = safe_orchestrator_run(self.agent, mysql_input)
            action_instance = mysql_output.action
            reasoning = mysql_output.reasoning
            
            # Continue execution until final response
            max_iterations = 10
            iteration = 0
            
            while not isinstance(action_instance, FinalResponseSchema) and iteration < max_iterations:
                schema_type = type(action_instance)
                ToolClass = tool_schema_to_class_map.get(schema_type)
                
                if not ToolClass:
                    logger.error(f"Unknown schema type: {schema_type.__name__}")
                    break
                
                tool_name = getattr(ToolClass, 'mcp_tool_name', 'unknown_tool')
                logger.debug(f"Executing MySQL tool: {tool_name} (iteration {iteration+1})")
                
                tool_instance = ToolClass()
                tool_output = tool_instance.run(action_instance)
                
                # Add result to agent memory
                result_message = MCPOrchestratorInputSchema(
                    query=f"Tool {tool_name} executed with result: {tool_output.result}"
                )
                self.agent.memory.add_message("system", result_message)
                
                mysql_output = safe_orchestrator_run(self.agent)
                action_instance = mysql_output.action
                reasoning = mysql_output.reasoning
                iteration += 1
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            # Process final result
            if isinstance(action_instance, FinalResponseSchema):
                result = {
                    "response": action_instance.response_text,
                    "reasoning": reasoning,
                    "iterations": iteration,
                    "query": query
                }
                
                return AgentOutputSchema(
                    success=True,
                    result=result,
                    execution_time=execution_time,
                    metadata={
                        "agent_type": "mysql_analyzer",
                        "iterations_used": iteration,
                        "max_iterations": max_iterations
                    }
                )
            else:
                return AgentOutputSchema(
                    success=False,
                    result={"response": f"Analysis incomplete after {max_iterations} iterations"},
                    execution_time=execution_time,
                    error="MySQL analysis did not complete within iteration limit",
                    metadata={"iterations_attempted": iteration}
                )
                
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"MySQL agent execution failed: {e}")
            
            return AgentOutputSchema(
                success=False,
                result={"error": str(e)},
                execution_time=execution_time,
                error=f"MySQL agent execution failed: {str(e)}"
            )
    
    def get_agent_capabilities(self) -> Dict[str, Any]:
        """Get MySQL agent capabilities."""
        return {
            "agent_type": "mysql_analyzer",
            "capabilities": [
                "historical_data_analysis",
                "supplier_analysis", 
                "customer_data_queries",
                "inventory_tracking",
                "order_status_queries"
            ],
            "input_types": ["financial_queries", "sql_requests", "entity_lookups"],
            "output_types": ["structured_data", "analysis_text", "entity_information"],
            "mcp_servers": ["mysql"],
            "timeout_default": 120,
            "retry_supported": True
        }


class ShortageAgentInterface:
    """Interface adapter for shortage analyzer agent orchestration."""
    
    def __init__(self, shortage_agent: Any):
        """Initialize with existing shortage agent instance."""
        self.agent = shortage_agent
        self.agent_name = "shortage_analyzer"
        self._initialized = False
        logger.info(f"ShortageAgentInterface created for {self.agent_name}")
    
    async def initialize_for_orchestration(self) -> bool:
        """Initialize shortage agent for orchestrated execution."""
        try:
            if hasattr(self.agent, 'initialize_llm') and not self._initialized:
                await self.agent.initialize_llm()
                self._initialized = True
                logger.info("Shortage agent initialized for orchestration")
            return True
        except Exception as e:
            logger.error(f"Shortage agent initialization failed: {e}")
            return False
    
    async def execute_orchestrated(self, input_data: AgentInputSchema) -> AgentOutputSchema:
        """Execute shortage analysis with orchestration context."""
        start_time = asyncio.get_event_loop().time()
        
        try:
            if not self._initialized:
                await self.initialize_for_orchestration()
            
            query = input_data.query
            context = input_data.context
            parameters = input_data.parameters
            
            # Prepare shortage analysis input
            company_name = parameters.get("company_name", "OrchestrationAnalysis")
            
            # Combine query with context
            enhanced_message = "Analyze component shortage risk using MCP SSE transport"
            if context.shared_context:
                enhanced_message += f"\n\nWorkflow Context: {context.shared_context}"
            
            shortage_input = {
                "company_name": company_name,
                "financial_data": query,
                "message": enhanced_message,
                "components": parameters.get("components", {}),
                "required_quantities": parameters.get("required_quantities", []),
                "available_quantities": parameters.get("available_quantities", []),
                "weights": parameters.get("weights", [])
            }
            
            # Execute shortage analysis
            result = await self.agent.enhanced_shortage_analysis(shortage_input)
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            # Transform result to standard format
            shortage_result = {
                "company_name": result.company_name,
                "shortage_index": result.shortage_index,
                "risk_level": result.risk_level,
                "response": result.response,
                "recommendations": getattr(result, 'recommendations', []),
                "query": query
            }
            
            return AgentOutputSchema(
                success=True,
                result=shortage_result,
                execution_time=execution_time,
                metadata={
                    "agent_type": "shortage_analyzer",
                    "company_analyzed": company_name,
                    "risk_classification": result.risk_level
                }
            )
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"Shortage agent execution failed: {e}")
            
            return AgentOutputSchema(
                success=False,
                result={"error": str(e)},
                execution_time=execution_time,
                error=f"Shortage agent execution failed: {str(e)}"
            )
    
    def get_agent_capabilities(self) -> Dict[str, Any]:
        """Get shortage agent capabilities."""
        return {
            "agent_type": "shortage_analyzer",
            "capabilities": [
                "shortage_index_calculation",
                "risk_level_assessment",
                "component_analysis",
                "supplier_risk_weighting",
                "recommendation_generation"
            ],
            "input_types": ["financial_data", "component_specifications", "inventory_data"],
            "output_types": ["shortage_indices", "risk_assessments", "recommendations"],
            "mcp_servers": ["shortage-index"],
            "timeout_default": 90,
            "retry_supported": True
        }


class AlertAgentInterface:
    """Interface adapter for alert manager agent orchestration."""
    
    def __init__(self, alert_agent: Any):
        """Initialize with existing alert agent instance."""
        self.agent = alert_agent
        self.agent_name = "alert_manager"
        self._initialized = False
        logger.info(f"AlertAgentInterface created for {self.agent_name}")
    
    async def initialize_for_orchestration(self) -> bool:
        """Initialize alert agent for orchestrated execution."""
        try:
            if hasattr(self.agent, 'initialize_llm') and not self._initialized:
                await self.agent.initialize_llm()
                self._initialized = True
                logger.info("Alert agent initialized for orchestration")
            return True
        except Exception as e:
            logger.error(f"Alert agent initialization failed: {e}")
            return False
    
    async def execute_orchestrated(self, input_data: AgentInputSchema) -> AgentOutputSchema:
        """Execute alert management with orchestration context."""
        start_time = asyncio.get_event_loop().time()
        
        try:
            if not self._initialized:
                await self.initialize_for_orchestration()
            
            from schemas.agent_schemas import AlertManagementInputSchema
            
            query = input_data.query
            context = input_data.context
            parameters = input_data.parameters
            
            # Prepare alert management input
            company_name = parameters.get("company_name", "OrchestrationAnalysis")
            
            # Extract shortage data from context or parameters
            shortage_data = parameters.get("shortage_data", "")
            if not shortage_data and context.previous_results:
                # Try to extract from previous shortage analysis
                for step_id, step_result in context.previous_results.items():
                    if "shortage" in step_id and "result" in step_result:
                        result_data = step_result["result"]
                        if "shortage_index" in result_data and "risk_level" in result_data:
                            shortage_data = (
                                f"shortage_index is {result_data['shortage_index']:.3f}, "
                                f"risk_level is {result_data['risk_level']}"
                            )
                            break
            
            alert_input = AlertManagementInputSchema(
                company_name=company_name,
                analysis_data=context.shared_context or query,
                shortage_data=shortage_data,
                alert_message=parameters.get("alert_message", f"Orchestrated analysis alert: {query}"),
                message=f"Process orchestrated workflow results and send notifications for workflow {context.workflow_id}"
            )
            
            # Execute alert processing
            alert_result = await self.agent.process_financial_analysis(alert_input)
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            # Transform result to standard format
            alert_response = {
                "company_name": company_name,
                "alerts_sent": alert_result.alerts_sent,
                "notification_results": alert_result.notification_results,
                "alert_summary": alert_result.alert_summary,
                "query": query
            }
            
            return AgentOutputSchema(
                success=True,
                result=alert_response,
                execution_time=execution_time,
                metadata={
                    "agent_type": "alert_manager",
                    "alerts_count": len(alert_result.alerts_sent),
                    "notifications_count": len(alert_result.notification_results)
                }
            )
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"Alert agent execution failed: {e}")
            
            # For alert failures, we often want to continue the workflow
            # so we return success=True with error information
            return AgentOutputSchema(
                success=True,  # Allow workflow to continue
                result={
                    "alerts_sent": [],
                    "notification_results": [],
                    "alert_summary": f"Alert processing encountered issues: {str(e)}",
                    "error": str(e)
                },
                execution_time=execution_time,
                warnings=[f"Alert processing failed: {str(e)}"]
            )
    
    def get_agent_capabilities(self) -> Dict[str, Any]:
        """Get alert agent capabilities."""
        return {
            "agent_type": "alert_manager",
            "capabilities": [
                "alert_generation",
                "notification_delivery",
                "priority_assessment",
                "multi_channel_messaging",
                "delivery_tracking"
            ],
            "input_types": ["analysis_results", "alert_criteria", "notification_preferences"],
            "output_types": ["alert_summaries", "delivery_confirmations", "notification_logs"],
            "mcp_servers": ["alert-notification"],
            "timeout_default": 60,
            "retry_supported": False  # Alerts should not be retried to avoid duplicates
        }


class AgentOrchestrationManager:
    """Manager for orchestrating multiple agent interfaces."""
    
    def __init__(self):
        """Initialize the agent orchestration manager."""
        self.registered_agents: Dict[str, Any] = {}
        self.agent_interfaces: Dict[str, Any] = {}
        self.execution_history: List[Dict[str, Any]] = []
        logger.info("AgentOrchestrationManager initialized")
    
    def register_agent(self, agent_name: str, agent_instance: Any) -> None:
        """Register an agent for orchestration."""
        self.registered_agents[agent_name] = agent_instance
        
        # Create appropriate interface
        if agent_name == "mysql_analyzer":
            interface = MySQLAgentInterface(agent_instance)
        elif agent_name == "shortage_analyzer":
            interface = ShortageAgentInterface(agent_instance)
        elif agent_name == "alert_manager":
            interface = AlertAgentInterface(agent_instance)
        else:
            raise ValueError(f"No interface available for agent type: {agent_name}")
        
        self.agent_interfaces[agent_name] = interface
        logger.info(f"Registered agent {agent_name} with orchestration interface")
    
    async def initialize_all_agents(self) -> Dict[str, bool]:
        """Initialize all registered agents for orchestration."""
        results = {}
        
        for agent_name, interface in self.agent_interfaces.items():
            try:
                success = await interface.initialize_for_orchestration()
                results[agent_name] = success
                logger.info(f"Agent {agent_name} initialization: {'success' if success else 'failed'}")
            except Exception as e:
                logger.error(f"Agent {agent_name} initialization failed: {e}")
                results[agent_name] = False
        
        return results
    
    async def execute_agent(
        self,
        agent_name: str,
        query: str,
        context: AgentExecutionContext,
        parameters: Optional[Dict[str, Any]] = None
    ) -> AgentOutputSchema:
        """Execute a specific agent with orchestration context."""
        if agent_name not in self.agent_interfaces:
            raise ValueError(f"Agent {agent_name} not registered")
        
        interface = self.agent_interfaces[agent_name]
        
        input_data = AgentInputSchema(
            query=query,
            context=context,
            parameters=parameters or {}
        )
        
        # Execute agent
        result = await interface.execute_orchestrated(input_data)
        
        # Record execution
        execution_record = {
            "timestamp": datetime.now().isoformat(),
            "workflow_id": context.workflow_id,
            "agent_name": agent_name,
            "success": result.success,
            "execution_time": result.execution_time,
            "error": result.error
        }
        self.execution_history.append(execution_record)
        
        return result
    
    def get_agent_capabilities(self, agent_name: str) -> Dict[str, Any]:
        """Get capabilities for a specific agent."""
        if agent_name not in self.agent_interfaces:
            raise ValueError(f"Agent {agent_name} not registered")
        
        return self.agent_interfaces[agent_name].get_agent_capabilities()
    
    def get_all_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """Get capabilities for all registered agents."""
        capabilities = {}
        
        for agent_name, interface in self.agent_interfaces.items():
            capabilities[agent_name] = interface.get_agent_capabilities()
        
        return capabilities
    
    def get_execution_history(self, workflow_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get execution history, optionally filtered by workflow ID."""
        if workflow_id:
            return [record for record in self.execution_history 
                   if record["workflow_id"] == workflow_id]
        return self.execution_history.copy()
    
    def clear_execution_history(self) -> None:
        """Clear execution history."""
        self.execution_history.clear()
        logger.info("Execution history cleared")