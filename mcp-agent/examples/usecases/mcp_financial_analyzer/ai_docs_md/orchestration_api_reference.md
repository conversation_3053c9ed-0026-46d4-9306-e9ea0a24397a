# MCP Financial Analyzer - Orchestration API Reference

## Table of Contents
1. [Main API Classes](#main-api-classes)
2. [Data Structures](#data-structures)
3. [Enums and Constants](#enums-and-constants)
4. [Configuration Classes](#configuration-classes)
5. [Exception Classes](#exception-classes)
6. [Usage Examples](#usage-examples)
7. [Type Definitions](#type-definitions)

## Main API Classes

### OrchestrationRunner

The primary interface for executing orchestrated financial analysis workflows.

```python
class OrchestrationRunner:
    def __init__(
        self,
        mysql_agent: Any,
        shortage_agent: Any,
        alert_agent: Any,
        llm_factory: Any,
        context_dir: Optional[str] = None,
        persist_context: bool = True
    )
```

#### Methods

##### `execute_financial_query()`
Execute a financial analysis query using the orchestrated agent system.

```python
async def execute_financial_query(
    self,
    query: str,
    workflow_id: Optional[str] = None,
    execution_mode: str = "pattern_based",  # "pattern_based" or "orchestrator_based"
    **kwargs
) -> Dict[str, Any]
```

**Parameters:**
- `query` (str): Natural language financial analysis query
- `workflow_id` (Optional[str]): Optional workflow ID (generated if not provided)
- `execution_mode` (str): Execution mode ("pattern_based" or "orchestrator_based")
- `**kwargs`: Additional parameters

**Returns:**
- `Dict[str, Any]`: Comprehensive analysis results dictionary

**Example Response:**
```json
{
    "success": true,
    "workflow_id": "workflow_abc123",
    "execution_time": 15.7,
    "execution_mode": "pattern_based",
    "query_analysis": {
        "type": "shortage_analysis",
        "confidence": 0.92,
        "complexity": 0.65,
        "workflow_pattern": "full_workflow"
    },
    "mysql_analysis": {
        "success": true,
        "response": "Analysis results...",
        "execution_time": 5.2
    },
    "shortage_analysis": {
        "success": true,
        "shortage_index": 0.75,
        "risk_level": "HIGH",
        "execution_time": 8.1
    },
    "alert_management": {
        "success": true,
        "alerts_sent": ["alert_1", "alert_2"],
        "execution_time": 2.4
    }
}
```

##### `health_check()`
Perform comprehensive health check of the orchestration system.

```python
async def health_check() -> Dict[str, Any]
```

**Returns:**
- `Dict[str, Any]`: Health status of all components

**Example Response:**
```json
{
    "status": "healthy",
    "timestamp": "2025-08-21T10:30:00.000Z",
    "components": {
        "context_manager": {
            "status": "healthy",
            "active_contexts": 3,
            "persist_enabled": true
        },
        "query_processor": {
            "status": "healthy",
            "supported_types": 8,
            "patterns_loaded": 15
        },
        "agents": {
            "mysql_analyzer": true,
            "shortage_analyzer": true,
            "alert_manager": true
        }
    },
    "statistics": {
        "total_workflows": 45,
        "successful_workflows": 42,
        "average_execution_time": 12.3
    }
}
```

##### `get_execution_statistics()`
Get execution statistics for monitoring and optimization.

```python
def get_execution_statistics() -> Dict[str, Any]
```

##### `validate_agent_connectivity()`
Validate that all agents are properly connected and functional.

```python
async def validate_agent_connectivity() -> Dict[str, bool]
```

---

### FinancialQueryProcessor

Intelligent query parsing and routing system.

```python
class FinancialQueryProcessor:
    def __init__(self)
```

#### Methods

##### `process_query()`
Process a financial query and return structured analysis.

```python
def process_query(self, query: str) -> ParsedQuery
```

**Parameters:**
- `query` (str): Natural language financial analysis query

**Returns:**
- `ParsedQuery`: Complete parsed query structure

##### `is_query_clear()`
Determine if the query is clear enough for execution.

```python
def is_query_clear(self, parsed: ParsedQuery) -> bool
```

##### `get_execution_plan()`
Generate an execution plan based on parsed query.

```python
def get_execution_plan(self, parsed: ParsedQuery) -> Dict[str, Any]
```

---

### ContextManager

Advanced context management for workflow data sharing.

```python
class ContextManager:
    def __init__(
        self, 
        persist_context: bool = True, 
        context_dir: Optional[Path] = None
    )
```

#### Methods

##### `create_context()`
Create a new workflow context.

```python
def create_context(
    self,
    workflow_id: str,
    query: str,
    query_type: str,
    workflow_pattern: str
) -> FinancialWorkflowContext
```

##### `update_mysql_context()`
Update context with MySQL agent results.

```python
def update_mysql_context(
    self,
    workflow_id: str,
    mysql_data: Dict[str, Any],
    execution_time: float = 0.0
) -> None
```

##### `update_shortage_context()`
Update context with shortage analyzer results.

```python
def update_shortage_context(
    self,
    workflow_id: str,
    shortage_data: Dict[str, Any],
    execution_time: float = 0.0
) -> None
```

##### `update_alert_context()`
Update context with alert manager results.

```python
def update_alert_context(
    self,
    workflow_id: str,
    alert_data: Dict[str, Any],
    execution_time: float = 0.0
) -> None
```

##### `get_context_for_agent()`
Get formatted context data for a specific agent.

```python
def get_context_for_agent(
    self,
    workflow_id: str,
    agent_type: str,
    include_full_history: bool = True
) -> str
```

##### `get_full_context_summary()`
Get complete context summary for final reporting.

```python
def get_full_context_summary(self, workflow_id: str) -> Dict[str, Any]
```

---

### WorkflowPatternRegistry

Registry of predefined workflow patterns.

```python
class WorkflowPatternRegistry:
    def __init__(self)
```

#### Methods

##### `get_pattern()`
Get a workflow pattern by ID.

```python
def get_pattern(self, pattern_id: str) -> Optional[WorkflowPattern]
```

##### `get_all_patterns()`
Get all registered workflow patterns.

```python
def get_all_patterns() -> Dict[str, WorkflowPattern]
```

##### `get_pattern_for_query_type()`
Get the most appropriate pattern for a query type.

```python
def get_pattern_for_query_type(self, query_type: str) -> Optional[WorkflowPattern]
```

---

## Data Structures

### ParsedQuery

Complete parsed query structure with analysis results.

```python
@dataclass
class ParsedQuery:
    original_query: str
    query_type: QueryType
    confidence: float  # 0.0-1.0
    workflow_pattern: WorkflowPattern
    entities: EntityCollection
    parameters: QueryParameters
    keywords: List[str] = field(default_factory=list)
    intent_keywords: List[str] = field(default_factory=list)
    complexity_score: float = 0.0
    ambiguity_flags: List[str] = field(default_factory=list)
    suggested_clarifications: List[str] = field(default_factory=list)
```

### EntityCollection

Collection of extracted entities from query.

```python
@dataclass
class EntityCollection:
    orders: List[str] = field(default_factory=list)            # CUSTORD-YYYYMMXXX
    work_orders: List[str] = field(default_factory=list)       # WO-YYYYMMXXX  
    purchase_requests: List[str] = field(default_factory=list) # PR-YYYYMMXXX
    materials: List[str] = field(default_factory=list)         # MM2004, HCS500
    suppliers: List[str] = field(default_factory=list)         # MetaMind Technology
    customers: List[str] = field(default_factory=list)         # Tech Pioneer Co
    factories: List[str] = field(default_factory=list)         # Factory A
    products: List[str] = field(default_factory=list)          # G7B Golden_1
    
    def has_entities(self) -> bool
    def get_all_entities(self) -> Dict[str, List[str]]
```

### QueryParameters

Extracted parameters from financial queries.

```python
@dataclass
class QueryParameters:
    quantities: List[int] = field(default_factory=list)
    dates: List[str] = field(default_factory=list)
    thresholds: Dict[str, float] = field(default_factory=dict)
    priorities: List[str] = field(default_factory=list)
    time_ranges: List[str] = field(default_factory=list)
    risk_levels: List[str] = field(default_factory=list)
    currencies: List[str] = field(default_factory=list)
    percentages: List[float] = field(default_factory=list)
```

### FinancialWorkflowContext

Complete context data for a financial analysis workflow.

```python
class FinancialWorkflowContext(BaseModel):
    workflow_id: str
    original_query: str
    query_type: str
    workflow_pattern: str
    
    # Agent-specific context data
    mysql_context: Optional[MySQLContextData] = None
    shortage_context: Optional[ShortageContextData] = None
    alert_context: Optional[AlertContextData] = None
    
    # Workflow metadata
    metadata: ContextMetadata = Field(default_factory=ContextMetadata)
    
    # Execution tracking
    current_step: int = 0
    total_steps: int = 3
    is_complete: bool = False
    
    # Error handling
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
```

### MySQLContextData

Structured data from MySQL agent analysis.

```python
class MySQLContextData(BaseModel):
    query: str = Field(description="Original SQL query or analysis request")
    response: str = Field(description="MySQL agent response text")
    reasoning: str = Field(default="", description="Agent reasoning process")
    table_data: Optional[Dict[str, Any]] = Field(default=None)
    entities_found: Dict[str, List[str]] = Field(default_factory=dict)
    success: bool = Field(default=True)
    execution_time: float = Field(default=0.0)
    error: Optional[str] = Field(default=None)
```

### ShortageContextData

Structured data from shortage analyzer agent.

```python
class ShortageContextData(BaseModel):
    company_name: str
    shortage_index: float = Field(description="Calculated shortage index (0.0-1.0)")
    risk_level: str = Field(description="Risk classification (LOW/MEDIUM/HIGH)")
    weighted_shortage_index: Optional[float] = None
    components_analyzed: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    recommendations: List[str] = Field(default_factory=list)
    response: str
    mcp_service_status: str = "unknown"
    calculation_method: str = "fallback"
    success: bool = True
    execution_time: float = 0.0
    error: Optional[str] = None
```

### AlertContextData

Structured data from alert manager agent.

```python
class AlertContextData(BaseModel):
    company_name: str
    alerts_sent: List[str] = Field(default_factory=list)
    notification_results: List[str] = Field(default_factory=list)
    alert_summary: str
    channels_used: List[str] = Field(default_factory=list)
    severity_level: str = "medium"
    success: bool = True
    execution_time: float = 0.0
    error: Optional[str] = None
```

### WorkflowPattern

Complete workflow pattern definition.

```python
@dataclass
class WorkflowPattern:
    pattern_id: str
    name: str
    description: str
    steps: List[WorkflowStep]
    execution_mode: ExecutionMode = ExecutionMode.SEQUENTIAL
    estimated_duration: int = 300  # Seconds
    success_criteria: List[str] = field(default_factory=list)
    failure_recovery: str = "graceful_degradation"
    context_sharing: bool = True
    
    def get_execution_order(self) -> List[List[str]]
    def get_step(self, step_id: str) -> Optional[WorkflowStep]
```

### WorkflowStep

Individual step in a workflow pattern.

```python
@dataclass
class WorkflowStep:
    step_id: str
    agent_name: str
    description: str
    required_inputs: List[str] = field(default_factory=list)
    expected_outputs: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)  # Other step IDs
    parallel_with: List[str] = field(default_factory=list) # Steps for parallel execution
    timeout: int = 120  # Timeout in seconds
    retry_count: int = 2
    optional: bool = False  # Can workflow continue if this step fails?
    
    def can_execute(self, completed_steps: List[str]) -> bool
```

## Enums and Constants

### QueryType

Financial query classification types.

```python
class QueryType(Enum):
    SHORTAGE_ANALYSIS = "shortage_analysis"
    SUPPLIER_RISK = "supplier_risk"
    CUSTOMER_PRIORITY = "customer_priority"
    INVENTORY_CHECK = "inventory_check"
    ORDER_STATUS = "order_status"
    PRODUCTION_PLANNING = "production_planning"
    COST_ANALYSIS = "cost_analysis"
    COMPREHENSIVE = "comprehensive"
    UNKNOWN = "unknown"
```

### WorkflowPattern (Enum)

Predefined workflow execution patterns.

```python
class WorkflowPattern(Enum):
    MYSQL_ONLY = "mysql_only"          # MySQL → Results
    MYSQL_SHORTAGE = "mysql_shortage"  # MySQL → Shortage → Results
    MYSQL_ALERT = "mysql_alert"        # MySQL → Alert → Results
    FULL_WORKFLOW = "full_workflow"    # MySQL → Shortage → Alert → Results
    SHORTAGE_ONLY = "shortage_only"    # Shortage → Results
    ALERT_ONLY = "alert_only"          # Alert → Results
```

### ExecutionMode

Workflow execution mode options.

```python
class ExecutionMode(Enum):
    SEQUENTIAL = "sequential"    # Execute agents one after another
    PARALLEL = "parallel"       # Execute independent agents concurrently
    CONDITIONAL = "conditional" # Execute based on previous results
    HYBRID = "hybrid"           # Mix of sequential and parallel execution
```

## Configuration Classes

### FinancialOrchestratorConfig

Complete configuration for financial orchestrator system.

```python
class FinancialOrchestratorConfig(BaseModel):
    # MCP server configurations
    mcp_servers: Dict[str, MCPServerConfig] = Field(default_factory=dict)
    
    # Agent configurations  
    agents: Dict[str, AgentConfig] = Field(default_factory=dict)
    
    # Orchestration settings
    orchestration: OrchestrationConfig = Field(default_factory=OrchestrationConfig)
    
    # Performance settings
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    
    # Logging settings
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    
    # Environment and metadata
    environment: str = Field(default="development")
    version: str = Field(default="1.0.0")
```

### MCPServerConfig

Configuration for individual MCP servers.

```python
@dataclass
class MCPServerConfig:
    name: str
    transport: str = "sse"
    url: str = ""
    host: str = "localhost"
    port: int = 8000
    timeout: int = 120
    retry_count: int = 2
    health_endpoint: str = "/sse"
    
    @property
    def full_url(self) -> str
    @property 
    def health_url(self) -> str
```

### AgentConfig

Configuration for individual agents.

```python
@dataclass
class AgentConfig:
    name: str
    agent_type: str
    timeout: int = 120
    retry_count: int = 2
    optional: bool = False
    initialization_required: bool = True
    capabilities: List[str] = field(default_factory=list)
    mcp_servers: List[str] = field(default_factory=list)
```

### OrchestrationConfig

Configuration for orchestration system.

```python
@dataclass
class OrchestrationConfig:
    execution_mode: ExecutionMode = ExecutionMode.PATTERN_BASED
    default_workflow_pattern: str = "comprehensive"
    max_concurrent_workflows: int = 10
    context_persistence: bool = True
    context_directory: str = "./workflow_contexts"
    query_confidence_threshold: float = 0.6
    performance_monitoring: bool = True
    enable_caching: bool = True
    cache_ttl_seconds: int = 3600
```

## Exception Classes

### OrchestrationError

Base exception for orchestration system errors.

```python
class OrchestrationError(Exception):
    def __init__(self, message: str, workflow_id: str = None, error_code: str = None)
```

### QueryProcessingError

Exception for query processing failures.

```python
class QueryProcessingError(OrchestrationError):
    def __init__(self, message: str, query: str = None, confidence: float = None)
```

### ContextError

Exception for context management failures.

```python
class ContextError(OrchestrationError):
    def __init__(self, message: str, workflow_id: str = None, context_type: str = None)
```

### AgentExecutionError

Exception for agent execution failures.

```python
class AgentExecutionError(OrchestrationError):
    def __init__(self, message: str, agent_name: str = None, step_id: str = None)
```

## Usage Examples

### Basic Orchestration

```python
from orchestrator import create_orchestration_runner

# Create orchestration runner
runner = create_orchestration_runner(
    mysql_agent=mysql_agent,
    shortage_agent=shortage_agent,
    alert_agent=alert_agent,
    llm_factory=llm_factory
)

# Execute financial analysis query
result = await runner.execute_financial_query(
    query="Analyze shortage for order CUSTORD-202506001",
    execution_mode="pattern_based"
)

# Check results
if result["success"]:
    print(f"Query Type: {result['query_analysis']['type']}")
    print(f"Shortage Index: {result['shortage_analysis']['shortage_index']}")
else:
    print(f"Error: {result['error']}")
```

### Advanced Query Processing

```python
from orchestrator import FinancialQueryProcessor

# Create query processor
processor = FinancialQueryProcessor()

# Parse complex query
query = "Analyze supplier MetaMind Technology delivering DEP9005 CPUs for G8D servers with production delays affecting orders CUSTORD-202506001 and CUSTORD-202506002"
parsed = processor.process_query(query)

# Examine parsed results
print(f"Query Type: {parsed.query_type.value}")
print(f"Confidence: {parsed.confidence:.2f}")
print(f"Entities Found: {parsed.entities.get_all_entities()}")
print(f"Workflow Pattern: {parsed.workflow_pattern.value}")

# Check if clarification needed
if not processor.is_query_clear(parsed):
    for clarification in parsed.suggested_clarifications:
        print(f"Clarification needed: {clarification}")
```

### Context Management

```python
from orchestrator import ContextManager

# Create context manager
context_mgr = ContextManager(persist_context=True)

# Create workflow context
context = context_mgr.create_context(
    workflow_id="workflow_001",
    query="Analyze inventory shortage",
    query_type="shortage_analysis",
    workflow_pattern="full_workflow"
)

# Update with agent results
mysql_data = {"response": "Historical data shows...", "success": True}
context_mgr.update_mysql_context("workflow_001", mysql_data, 2.5)

shortage_data = {"shortage_index": 0.75, "risk_level": "HIGH", "success": True}
context_mgr.update_shortage_context("workflow_001", shortage_data, 3.2)

# Get context for next agent
alert_context = context_mgr.get_context_for_agent("workflow_001", "alert_manager")
print(f"Context for Alert Manager:\n{alert_context}")

# Get final summary
summary = context_mgr.get_full_context_summary("workflow_001")
print(f"Workflow Summary: {summary}")
```

### Custom Workflow Pattern

```python
from orchestrator import WorkflowPatternRegistry, WorkflowPattern, WorkflowStep, ExecutionMode

# Create custom workflow pattern
custom_pattern = WorkflowPattern(
    pattern_id="custom_analysis",
    name="Custom Financial Analysis",
    description="Custom workflow for specialized analysis",
    steps=[
        WorkflowStep(
            step_id="data_gathering",
            agent_name="mysql_analyzer",
            description="Gather comprehensive financial data",
            timeout=180,
            retry_count=3
        ),
        WorkflowStep(
            step_id="risk_assessment",
            agent_name="shortage_analyzer", 
            description="Assess risk factors",
            dependencies=["data_gathering"],
            timeout=120
        )
    ],
    execution_mode=ExecutionMode.SEQUENTIAL,
    estimated_duration=300
)

# Register custom pattern
registry = WorkflowPatternRegistry()
registry.register_pattern(custom_pattern)

# Use custom pattern
pattern = registry.get_pattern("custom_analysis")
execution_order = pattern.get_execution_order()
print(f"Execution Order: {execution_order}")
```

### Configuration Management

```python
from orchestrator.config import ConfigurationManager, FinancialOrchestratorConfig

# Load configuration
config_mgr = ConfigurationManager()
config = config_mgr.load_config()

# Access configuration values
mysql_config = config_mgr.get_mcp_server_config("mysql")
print(f"MySQL Server: {mysql_config.host}:{mysql_config.port}")

# Update configuration
updates = {
    "orchestration": {
        "max_concurrent_workflows": 20,
        "query_confidence_threshold": 0.7
    }
}
config_mgr.update_config(updates)

# Save configuration
config_mgr.save_config()

# Validate connectivity
connectivity = config_mgr.validate_connectivity()
print(f"Server Connectivity: {connectivity}")
```

### Error Handling

```python
from orchestrator import OrchestrationRunner
from orchestrator.exceptions import OrchestrationError, AgentExecutionError

runner = create_orchestration_runner(mysql_agent, shortage_agent, alert_agent, llm_factory)

try:
    result = await runner.execute_financial_query("Analyze shortage for invalid order")
    
    if not result["success"]:
        if result.get("requires_clarification"):
            print("Query needs clarification:")
            for clarification in result["suggested_clarifications"]:
                print(f"  - {clarification}")
        else:
            print(f"Execution failed: {result['error']}")
            
            # Check partial results
            if result.get("partial_results"):
                print("Partial results available:")
                print(result["partial_results"])

except AgentExecutionError as e:
    print(f"Agent execution failed: {e}")
    print(f"Agent: {e.agent_name}, Step: {e.step_id}")

except OrchestrationError as e:
    print(f"Orchestration error: {e}")
    print(f"Workflow ID: {e.workflow_id}")

except Exception as e:
    print(f"Unexpected error: {e}")
```

## Type Definitions

### Type Aliases

```python
from typing import Dict, List, Any, Optional, Union, Callable, Awaitable

# Common type aliases used throughout the API
WorkflowID = str
AgentName = str
StepID = str
QueryString = str
ContextData = Dict[str, Any]
ExecutionResult = Dict[str, Any]
AgentResult = Dict[str, Any]
ConfigurationData = Dict[str, Any]
HealthStatus = Dict[str, Any]
ExecutionStatistics = Dict[str, Any]

# Function type aliases
LLMFactory = Callable[[Agent], AugmentedLLM]
ContextFormatter = Callable[[FinancialWorkflowContext, str], str]
QueryClassifier = Callable[[str], Tuple[QueryType, float]]
EntityExtractor = Callable[[str], EntityCollection]
```

### Generic Types

```python
from typing import TypeVar, Generic

# Generic type variables
T = TypeVar('T')
AgentT = TypeVar('AgentT')
ContextT = TypeVar('ContextT')
ResultT = TypeVar('ResultT')

# Generic classes
class AgentInterface(Generic[AgentT]):
    agent: AgentT
    
class ContextManager(Generic[ContextT]):
    active_contexts: Dict[str, ContextT]

class ExecutionResult(Generic[ResultT]):
    success: bool
    result: Optional[ResultT]
    error: Optional[str]
```

---

This API reference provides comprehensive documentation for all public interfaces of the orchestration system. Use this reference to understand the available methods, parameters, return types, and usage patterns for integrating with the MCP Financial Analyzer orchestration system.