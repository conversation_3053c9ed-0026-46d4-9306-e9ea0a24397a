# VLLM Streaming Integration Test Guide

## Overview

This comprehensive test suite validates the VLLM streaming implementation with **real API integration** across all three stages of the financial analysis workflow. The tests use the actual VLLM server endpoint and do not rely on mocks or simulated responses.

## Prerequisites

### 1. VLLM Server Requirements
- VLLM server must be running at: `http://************:38701/v1`
- Model: `Qwen/Qwen3-32B` (or compatible model)
- Server must be accessible from the test environment

### 2. Dependencies
Install required dependencies:
```bash
pip install -r requirements.txt
```

Additional test dependencies:
```bash
pip install pytest pytest-asyncio aiohttp
```

## Test Suite Components

### Core Test File: `test_vllm_basic.py`

The main test file contains:

1. **VLLMStreamingTestSuite Class**: Comprehensive test orchestrator
2. **Real API Integration**: Uses actual VLLM HTTP endpoints
3. **Three Workflow Stages**: Research, Analysis, Report generation
4. **Streaming Validation**: Verifies real-time streaming behavior
5. **Error Handling**: Tests network failures and API errors
6. **Pytest Compatibility**: Individual test functions for pytest

### Test Runner: `run_vllm_tests.py`

Convenient script for running tests with various options.

## Running the Tests

### Option 1: Complete Test Suite
Run all tests with detailed output:
```bash
python test_vllm_basic.py
```

### Option 2: Using the Test Runner
```bash
# Run all tests
python run_vllm_tests.py

# Test connectivity only
python run_vllm_tests.py --connectivity

# Test specific stage
python run_vllm_tests.py --stage research
python run_vllm_tests.py --stage analysis
python run_vllm_tests.py --stage report

# Run with pytest
python run_vllm_tests.py --pytest
```

### Option 3: Direct Pytest
```bash
# Run all tests
pytest test_vllm_basic.py -v

# Run specific test
pytest test_vllm_basic.py::test_research_streaming -v

# Run with asyncio support
pytest test_vllm_basic.py -v --asyncio-mode=auto
```

## Test Stages Explained

### 1. Research Stage (`test_research_stage_streaming`)
- **Purpose**: Tests financial data gathering and initial analysis
- **Validation**: Checks for financial keywords, streaming chunks, response quality
- **Expected Output**: Stock prices, earnings data, market sentiment

### 2. Analysis Stage (`test_analysis_stage_streaming`)
- **Purpose**: Tests processing and computation of financial metrics
- **Validation**: Verifies analytical content, performance assessment
- **Expected Output**: Detailed financial analysis with recommendations

### 3. Report Stage (`test_report_stage_streaming`)
- **Purpose**: Tests final report generation and formatting
- **Validation**: Checks markdown formatting, comprehensive content
- **Expected Output**: Professional financial report with sections

### 4. Error Handling (`test_error_handling`)
- **Purpose**: Tests graceful handling of network/API failures
- **Validation**: Ensures fallback mechanisms work properly
- **Expected Behavior**: Graceful degradation with error messages

### 5. Streaming Interruption (`test_streaming_interruption`)
- **Purpose**: Tests handling of interrupted streaming connections
- **Validation**: Verifies proper cleanup and error handling
- **Expected Behavior**: Clean interruption handling

## Test Output and Results

### Console Output
The tests provide detailed console output with:
- 🔍 Connectivity verification
- 📊 Streaming chunk tracking
- ✅/❌ Validation results
- 📈 Performance metrics
- 📋 Detailed summaries

### Results File
Test results are automatically saved to:
```
vllm_streaming_test_results_YYYYMMDD_HHMMSS.json
```

Contains:
- Overall success status
- Individual test results
- Performance metrics
- Configuration details

## Validation Criteria

### Streaming Validation
- ✅ Multiple chunks received (real streaming)
- ✅ Reasonable response time (> 0.5s for real API)
- ✅ Progressive content delivery

### Content Quality
- ✅ Relevant financial keywords present
- ✅ Appropriate response length
- ✅ Proper formatting (for reports)

### Error Handling
- ✅ Graceful failure handling
- ✅ Fallback response provided
- ✅ Clean interruption management

## Troubleshooting

### Common Issues

1. **VLLM Server Not Accessible**
   ```
   ❌ Failed to connect to VLLM server: Connection refused
   ```
   - Verify server is running at `http://************:38701/v1`
   - Check network connectivity
   - Confirm firewall settings

2. **Model Not Available**
   ```
   ❌ Model 'Qwen/Qwen3-32B' not found
   ```
   - Check available models: `curl http://************:38701/v1/models`
   - Update `VLLM_MODEL` constant in test file

3. **Import Errors**
   ```
   ModuleNotFoundError: No module named 'session_manager'
   ```
   - Ensure you're running from the correct directory
   - Install dependencies: `pip install -r requirements.txt`

### Debug Mode
For detailed debugging, set environment variable:
```bash
export MCP_AGENT_LOG_LEVEL=debug
python test_vllm_basic.py
```

## Configuration

### VLLM Endpoint Configuration
Update these constants in `test_vllm_basic.py`:
```python
VLLM_API_BASE = "http://************:38701/v1"  # Your VLLM server
VLLM_MODEL = "Qwen/Qwen3-32B"                   # Your model
TEST_COMPANY = "Apple Inc."                      # Test company
```

### Test Customization
Modify validation criteria in the `_validate_*_response` methods to adjust:
- Required keywords
- Minimum response length
- Streaming chunk requirements
- Timing thresholds

## Integration with CI/CD

For automated testing, use:
```bash
# Exit with proper code for CI/CD
python test_vllm_basic.py
echo $?  # 0 for success, 1 for failure
```

Or with pytest:
```bash
pytest test_vllm_basic.py --junitxml=test-results.xml
```

## Support

For issues or questions:
1. Check VLLM server logs
2. Verify network connectivity
3. Review test output for specific error messages
4. Check the generated results JSON file for detailed metrics
