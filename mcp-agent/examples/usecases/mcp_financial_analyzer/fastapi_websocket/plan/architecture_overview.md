# Architecture Overview

## System Description

The VLLM Augmented LLM system follows a hierarchical design pattern with a base `VLLMAugmentedLLM` class that provides core functionality for LLM operations augmented with MCP (Model Context Protocol) tool execution capabilities. The `StreamingVLLMAugmentedLLM` subclass extends this foundation by adding real-time streaming capabilities through specialized callback mechanisms. The base class manages interactions with the LLM engine and MCP tool executor for standard request-response patterns, while the streaming subclass introduces additional attributes like `stream_callback` and `mcp_stream_callback` to handle real-time data flow. The WebSocket manager facilitates bidirectional communication for streaming scenarios, enabling responsive user experiences with progressive content delivery and live tool execution feedback.

## UML Class Diagram

```

            LLM Engine               │
  ┌─────────────────────────
  │  - model: str                   ││
  │  - config: Dict                 ││
  │  + generate(prompt): str        ││
  │  + stream_generate(prompt): Iterator││
  └─────────────

                    │
                    │ uses
                    ▼

        VLLMAugmentedLLM            │
  ┌─────────────────────────────────┐│
  - llm_engine: LLMEngine       ││  
  │  - mcp_executor: MCPExecutor   ││
  │  - context: Dict               ││
  │                                ││
  │  + process_request(query): str ││
  │  + execute_tools(tools): Dict  ││
  │  + augment_context(): None     ││
  └──────────────

                    │
                    │ inherits
                    ▼

    StreamingVLLMAugmentedLLM       │
  ┌─────────────────────────────────┐│
  │  + stream_callback: Callable   ││  ← New streaming attribute
  │  + mcp_stream_callback: Callable││ ← New MCP streaming attribute  
  │  + websocket_manager: WSManager││  ← WebSocket integration
  │  + buffer_size: int            ││  ← Stream buffer configuration
  │  + stream_timeout: float       ││  ← Timeout handling
  │                                ││
  │  + stream_process(query): Iterator││
  │  + handle_stream_chunk(): None ││
  │  + stream_tools(): Iterator    ││
  └─────────────────────────────────┘│

                    │
                    │ uses
                    ▼

         WebSocket Manager          │
  ┌────────────
  │  - connections: Set[WebSocket] ││
  │  - message_queue: Queue        ││
  │                                ││
  │  + broadcast(data): None       ││
  │  + send_to_client(id, data)    ││
  │  + handle_connection(): None   │
  └──────────



         MCP Tool Executor          │
  ┌──────────
  │  - tools: List[Tool]           ││
  │  - execution_context: Dict     ││
  │                                ││
  │  + execute(tool_name): Result  ││
  │  + get_available_tools(): List ││
  │  + validate_tool(tool): bool   ││
  └────────

                    ▲
                    │ uses
                    │
         ┌──────────┴──────────┐
         │                     │
   VLLMAugmentedLLM    StreamingVLLMAugmentedLLM


Relationships:
- VLLMAugmentedLLM uses LLM Engine for text generation
- VLLMAugmentedLLM uses MCP Tool Executor for tool operations  
- StreamingVLLMAugmentedLLM inherits from VLLMAugmentedLLM
- StreamingVLLMAugmentedLLM uses WebSocket Manager for real-time communication
- Both classes use MCP Tool Executor (inherited relationship)

Key New Attributes in StreamingVLLMAugmentedLLM:
 stream_callback: Handles real-time content streaming
 mcp_stream_callback: Manages streaming tool execution feedback
 websocket_manager: Facilitates WebSocket-based communication
 buffer_size: Controls streaming buffer management
 stream_timeout: Manages streaming operation timeouts
```
