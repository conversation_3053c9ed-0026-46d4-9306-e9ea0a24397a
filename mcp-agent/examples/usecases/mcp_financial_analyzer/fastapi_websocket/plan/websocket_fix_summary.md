# WebSocket Connection Error Fix Summary

## Original Error
```
2025-07-24 07:31:35,199 - ERROR - Failed to send error message to test_user_streaming:research:Apple Inc.: Cannot call "send" once a close message has been sent.
2025-07-24 07:31:35,199 - ERROR - Error in message loop for test_user_streaming:research:Apple Inc.: WebSocket is not connected. Need to call "accept" first.
```

## Root Cause
The error occurred because the code attempted to:
1. Receive data from a WebSocket that was already disconnected (`receive_text()` on closed connection)
2. Send error messages to connections that were already closed
3. Lack of consistent connection state checking before WebSocket operations

## Solutions Implemented

### 1. Helper Functions Added (lines 12-40)
```python
def is_websocket_connected(websocket: WebSocket) -> bool:
    """Check if WebSocket is connected and ready for communication."""
    try:
        return websocket.client_state.name == "CONNECTED"
    except AttributeError:
        return False

def log_websocket_state(websocket: WebSocket, connection_id: str, context: str = "") -> None:
    """Log current WebSocket connection state with detailed information."""
    try:
        state = websocket.client_state.name
        logger.debug(f"WebSocket state for {connection_id} {context}: {state}")
    except AttributeError:
        logger.debug(f"WebSocket state for {connection_id} {context}: UNKNOWN (no client_state)")
```

### 2. Pre-Receive Connection Checks (lines 562-565)
```python
# Check connection state before attempting to receive
log_websocket_state(websocket, connection_id, "before receive_text()")
if not is_websocket_connected(websocket):
    logger.warning(f"WebSocket disconnected for {connection_id}, exiting message loop")
    break
```

### 3. Enhanced Error Detection (lines 730-733)
```python
# Check if it's a WebSocket connection error specifically
if "WebSocket is not connected" in str(e) or "close message has been sent" in str(e):
    logger.warning(f"WebSocket connection lost for {connection_id}: {e}")
    break
```

### 4. Safe Error Message Sending (lines 735-743)
```python
try:
    if is_websocket_connected(websocket):
        error_msg = {"type": "error", "message": f"An error occurred: {str(e)}"}
        await websocket.send_text(json.dumps(error_msg))
    else:
        log_websocket_state(websocket, connection_id, "message loop error - connection lost, cannot send error")
except Exception as send_error:
    logger.error(f"Failed to send error message to {connection_id}: {send_error}")
    log_websocket_state(websocket, connection_id, "after error message send failure")
```

### 5. Stream Callbacks Protection
All streaming callbacks now check connection state:
- LLM stream callback (lines 609-620)
- MCP stream callback (lines 625-636)
- Stream end message (lines 649-663)

### 6. Enhanced Lifecycle Logging
- Connection acceptance (line 538)
- Welcome message sent (line 559)  
- Session cleanup (line 766)
- All error scenarios (22 total log points)

## Key Improvements

1. **Prevention**: Connection state checked before ALL WebSocket operations
2. **Early Detection**: Specific error message detection breaks loops immediately
3. **Graceful Degradation**: Failed sends don't crash the application
4. **Enhanced Visibility**: 22 logging points track connection lifecycle
5. **Consistent Pattern**: Same helper functions used throughout codebase

## Usage Statistics
- `is_websocket_connected()`: Used 10 times throughout the code
- `log_websocket_state()`: Used 22 times for comprehensive logging
- Connection state checks added to all critical WebSocket operations

## Testing
- Helper functions validated with mock WebSocket objects
- Error detection patterns tested for both original error messages
- All syntax validated and imports verified

This fix should completely resolve the "Cannot call send once a close message has been sent" and "WebSocket is not connected" errors by preventing operations on disconnected WebSockets and providing detailed logging for troubleshooting.