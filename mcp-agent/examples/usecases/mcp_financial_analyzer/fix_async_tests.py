#!/usr/bin/env python3
"""
Script to add @pytest.mark.asyncio decorators to async test functions.
"""

import os
import re
from pathlib import Path

def fix_async_tests_in_file(file_path):
    """Add @pytest.mark.asyncio decorators to async test functions in a file."""
    print(f"Fixing async tests in {file_path}")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find async test functions that don't already have @pytest.mark.asyncio
    lines = content.split('\n')
    modified_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        
        # Check if this is an async test function
        if re.match(r'\s*async def test_', line):
            # Check if the previous line already has @pytest.mark.asyncio
            prev_line = lines[i-1] if i > 0 else ""
            if '@pytest.mark.asyncio' not in prev_line:
                # Get the indentation of the async def line
                indent = len(line) - len(line.lstrip())
                decorator_line = ' ' * indent + '@pytest.mark.asyncio'
                
                # Add the decorator before the async def
                modified_lines.append(decorator_line)
        
        modified_lines.append(line)
        i += 1
    
    # Write the modified content back
    modified_content = '\n'.join(modified_lines)
    
    with open(file_path, 'w') as f:
        f.write(modified_content)
    
    print(f"Fixed async tests in {file_path}")

def main():
    """Fix async tests in all test files."""
    test_dir = Path("tests/plan")
    
    if not test_dir.exists():
        print(f"Test directory {test_dir} does not exist")
        return
    
    test_files = list(test_dir.glob("test_*.py"))
    
    if not test_files:
        print("No test files found")
        return
    
    print(f"Found {len(test_files)} test files to fix")
    
    for test_file in test_files:
        try:
            fix_async_tests_in_file(test_file)
        except Exception as e:
            print(f"Error fixing {test_file}: {e}")
    
    print("Async test fixing complete!")

if __name__ == "__main__":
    main()
