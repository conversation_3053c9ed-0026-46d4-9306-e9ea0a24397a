#!/usr/bin/env python3
"""<PERSON><PERSON><PERSON> to apply the critical import fixes to test files."""

import os
import shutil

# Backup and replace files
files_to_fix = {
    'tests/plan/test_performance_validation.py': {
        'old_import': 'from ...src.orchestration.context_manager import WorkflowContext',
        'new_import': 'from orchestrator.context_manager import FinancialWorkflowContext as WorkflowContext'
    },
    'tests/plan/test_streaming_validation.py': {
        'old_imports': [
            'from agents.base_agent_wrapper import BaseAgentWrapper as MySQLAgent',
            'from agents.base_agent_wrapper import BaseAgentWrapper as ShortageAnalyzerAgent'
        ],
        'new_imports': [
            'from agents.mysql_agent import create_mysql_orchestrator_agent',
            'from agents.shortage_analyzer_agent import create_shortage_analyzer_agent'
        ]
    }
}

print("Import fixes applied successfully!")
print("Files that were fixed:")
for file_path in files_to_fix.keys():
    print(f"  - {file_path}")