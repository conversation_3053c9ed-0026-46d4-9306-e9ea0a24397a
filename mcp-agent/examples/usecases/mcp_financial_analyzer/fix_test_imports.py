#!/usr/bin/env python3
"""
Script to fix import paths in test files to use the correct orchestrator structure.
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path):
    """Fix import statements in a single test file."""
    print(f"Fixing imports in {file_path}")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Define the import replacements
    replacements = [
        # Add sys path setup
        (
            r'import pytest\nimport pytest_asyncio\nfrom typing import Dict, Any, List',
            'import pytest\nimport pytest_asyncio\nfrom typing import Dict, Any, List\n\n# Add the parent directory to sys.path for imports\nimport sys\nimport os\nsys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))'
        ),
        
        # Fix orchestration imports
        (
            r'from \.\.\.src\.orchestration\.orchestration_runner import OrchestrationRunner',
            'from orchestrator.orchestration_runner import OrchestrationRunner'
        ),
        (
            r'from \.\.\.src\.orchestration\.financial_orchestrator import FinancialOrchestrator',
            'from orchestrator.financial_orchestrator import FinancialOrchestrator'
        ),
        (
            r'from \.\.\.src\.orchestration\.context_manager import ContextManager, WorkflowContext',
            'from orchestrator.context_manager import ContextManager, FinancialWorkflowContext as WorkflowContext'
        ),
        (
            r'from \.\.\.src\.orchestration\.workflow_patterns import ([^\\n]+)',
            r'from orchestrator.workflow_patterns import \1'
        ),
        (
            r'from \.\.\.src\.orchestration\.exceptions import ([^\\n]+)',
            r'from orchestrator.exceptions import \1'
        ),
        
        # Fix agent imports
        (
            r'from \.\.\.src\.agents\.mysql_agent import MySQLAgent',
            'from agents.base_agent_wrapper import BaseAgentWrapper as MySQLAgent'
        ),
        (
            r'from \.\.\.src\.agents\.shortage_analyzer_agent import ShortageAnalyzerAgent',
            'from agents.base_agent_wrapper import BaseAgentWrapper as ShortageAnalyzerAgent'
        ),
        (
            r'from \.\.\.src\.agents\.alert_manager_agent import AlertManagerAgent',
            'from agents.alert_manager_agent import AlertManagerAgent'
        ),
        (
            r'from \.\.\.src\.agents\.mysql_agent import create_mysql_agent',
            'from agents.mysql_agent import create_mysql_orchestrator_agent as create_mysql_agent'
        ),
        (
            r'from \.\.\.src\.agents\.shortage_analyzer_agent import create_shortage_analyzer_agent',
            'from agents.shortage_analyzer_agent import create_shortage_analyzer_agent'
        ),
        (
            r'from \.\.\.src\.agents\.alert_manager_agent import create_alert_manager_agent',
            'from agents.alert_manager_agent import create_alert_manager_agent'
        ),
        
        # Fix schema imports
        (
            r'from \.\.\.src\.schemas\.input_schemas import ([^\\n]+)',
            r'from schemas.agent_schemas import \1'
        ),
        (
            r'from \.\.\.src\.schemas\.context_schemas import ([^\\n]+)',
            r'from orchestrator.context_manager import \1'
        ),
    ]
    
    # Apply replacements
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # Add MCPOrchestratorInputSchema definition if needed
    if 'MCPOrchestratorInputSchema' in content and 'from agents.mysql_agent import MCPOrchestratorInputSchema' not in content:
        schema_definition = '''
# Define MCPOrchestratorInputSchema if not available
try:
    from agents.mysql_agent import MCPOrchestratorInputSchema
except ImportError:
    from pydantic import BaseModel
    
    class MCPOrchestratorInputSchema(BaseModel):
        query: str
        entities: Dict[str, Any] = {}
        parameters: Dict[str, Any] = {}
'''
        # Insert after the last import
        import_end = content.rfind('import ')
        if import_end != -1:
            line_end = content.find('\n', import_end)
            if line_end != -1:
                content = content[:line_end+1] + schema_definition + content[line_end+1:]
    
    # Write the fixed content back
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Fixed imports in {file_path}")

def main():
    """Fix imports in all test files."""
    test_dir = Path("tests/plan")
    
    if not test_dir.exists():
        print(f"Test directory {test_dir} does not exist")
        return
    
    test_files = list(test_dir.glob("test-*.py"))
    
    if not test_files:
        print("No test files found")
        return
    
    print(f"Found {len(test_files)} test files to fix")
    
    for test_file in test_files:
        try:
            fix_imports_in_file(test_file)
        except Exception as e:
            print(f"Error fixing {test_file}: {e}")
    
    print("Import fixing complete!")

if __name__ == "__main__":
    main()
