"""
Orchestrated Financial Analysis Tool
===================================

Main entry point for the orchestrated financial analysis system that coordinates
MySQL Agent, Shortage Analyzer Agent, and Alert Manager Agent through an
intelligent orchestration layer.

Features:
- Intelligent query parsing and workflow routing
- Context-aware agent coordination
- Error handling and recovery
- Performance monitoring and statistics
- Comprehensive workflow patterns
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any, Optional

from mcp_agent.app import MCPApp
from mcp_agent.workflows.llm.augmented_llm import AugmentedLLM
from mcp_agent.agents.agent import Agent

# Import orchestration components
from orchestrator.orchestration_runner import create_orchestration_runner, OrchestrationRunner
from agents.agent_interfaces import AgentOrchestrationManager

# Import existing agents
from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from agents.alert_manager_agent import create_alert_manager_agent  
from agents.mysql_agent import create_mysql_orchestrator_agent

# Server management from existing main
from main import DualServerManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize MCP App
app = MCPApp(name="orchestrated_financial_analyzer", human_input_callback=None)


class OrchestrationDemo:
    """Demo class for orchestrated financial analysis workflows."""
    
    def __init__(self):
        """Initialize the orchestration demo."""
        self.server_manager = DualServerManager()
        self.orchestration_runner: Optional[OrchestrationRunner] = None
        self.agent_manager: Optional[AgentOrchestrationManager] = None
        
    async def initialize_system(self) -> bool:
        """Initialize the complete orchestration system."""
        logger.info("=== Initializing Orchestrated Financial Analysis System ===")
        
        # Step 1: Check server availability
        logger.info("Checking MCP server availability...")
        shortage_available, alert_available, mysql_available = await self.server_manager.check_all_servers_health()
        
        if not all([shortage_available, alert_available, mysql_available]):
            missing_servers = []
            if not shortage_available:
                missing_servers.append("shortage-index (6970)")
            if not alert_available:
                missing_servers.append("alert-notification (6972)")
            if not mysql_available:
                missing_servers.append("mysql (8702)")
            
            logger.error(f"Required MCP servers not available: {', '.join(missing_servers)}")
            self.server_manager.display_startup_instructions(
                shortage_needed=not shortage_available,
                alert_needed=not alert_available,
                mysql_needed=not mysql_available
            )
            return False
        
        logger.info("✓ All MCP servers are available")
        
        # Step 2: Initialize MCP App context
        logger.info("Initializing MCP App context...")
        async with app.run() as analyzer_app:
            context = analyzer_app.context
            logger.info("✓ MCP App context initialized")
            
            # Step 3: Create agents
            logger.info("Creating and initializing agents...")
            mysql_agent = create_mysql_orchestrator_agent()
            shortage_agent = create_shortage_analyzer_agent("OrchestrationDemo")
            alert_agent = create_alert_manager_agent("OrchestrationDemo")
            
            # Step 4: Initialize agents for MCP
            agents_to_init = [
                ("MySQL", mysql_agent),
                ("Shortage", shortage_agent), 
                ("Alert", alert_agent)
            ]
            
            for agent_name, agent in agents_to_init:
                if hasattr(agent, 'initialize_llm'):
                    try:
                        await agent.initialize_llm()
                        logger.info(f"✓ {agent_name} agent LLM initialized")
                    except Exception as e:
                        logger.error(f"✗ {agent_name} agent initialization failed: {e}")
                        return False
                else:
                    logger.warning(f"✗ {agent_name} agent does not support LLM initialization")
            
            # Step 5: Create LLM factory for orchestrator
            def llm_factory(agent: Agent) -> AugmentedLLM:
                """Factory function to create LLM instances."""
                # This is a simplified factory - in production you'd configure based on agent needs
                from mcp_agent.workflows.llm.anthropic_llm import AnthropicLLM
                from mcp_agent.workflows.llm.openai_llm import OpenAILLM
                
                try:
                    # Try to use the same LLM configuration as the agents
                    return OpenAILLM(agent=agent, context=context)
                except Exception:
                    # Fallback to basic configuration
                    logger.warning(f"Using fallback LLM configuration for {agent.name}")
                    return OpenAILLM(agent=agent, context=context)
            
            # Step 6: Create orchestration runner
            logger.info("Creating orchestration runner...")
            self.orchestration_runner = create_orchestration_runner(
                mysql_agent=mysql_agent,
                shortage_agent=shortage_agent,
                alert_agent=alert_agent,
                llm_factory=llm_factory,
                persist_context=True
            )
            
            # Step 7: Create agent manager for interface testing
            self.agent_manager = AgentOrchestrationManager()
            self.agent_manager.register_agent("mysql_analyzer", mysql_agent)
            self.agent_manager.register_agent("shortage_analyzer", shortage_agent)
            self.agent_manager.register_agent("alert_manager", alert_agent)
            
            # Initialize agent interfaces
            await self.agent_manager.initialize_all_agents()
            
            logger.info("✓ Orchestration system initialized successfully")
            return True
    
    async def run_health_check(self) -> Dict[str, Any]:
        """Run comprehensive health check of the orchestration system."""
        if not self.orchestration_runner:
            return {"status": "not_initialized", "error": "System not initialized"}
        
        logger.info("\n=== Orchestration System Health Check ===")
        health = await self.orchestration_runner.health_check()
        
        # Display health status
        logger.info(f"System Status: {health['status']}")
        logger.info(f"Components: {len(health['components'])}")
        
        for component, status in health.get('components', {}).items():
            if isinstance(status, dict):
                comp_status = status.get('status', 'unknown')
                logger.info(f"  {component}: {comp_status}")
            else:
                logger.info(f"  {component}: {status}")
        
        if health['status'] != 'healthy':
            logger.warning("⚠️ System health issues detected")
            if 'issues' in health:
                for issue in health['issues']:
                    logger.warning(f"  - {issue}")
        else:
            logger.info("✓ All systems healthy")
        
        return health
    
    async def run_demo_scenarios(self) -> None:
        """Run demonstration scenarios using the orchestrator."""
        if not self.orchestration_runner:
            logger.error("Orchestration system not initialized")
            return
        
        logger.info("\n=== Running Orchestration Demo Scenarios ===")
        
        # Demo scenarios with different query types
        demo_queries = [
            {
                "name": "Shortage Analysis Query",
                "query": "Analyze shortage risk for CUSTORD-202506001 requiring MM2004 80GB GPUs",
                "description": "Tests shortage analysis workflow pattern"
            },
            {
                "name": "Supplier Risk Query", 
                "query": "Assess supplier reliability for MetaMind Technology delivering DEP9005 CPUs",
                "description": "Tests supplier risk assessment workflow pattern"
            },
            {
                "name": "Customer Priority Query",
                "query": "Check delivery priorities for Tech Pioneer Co orders with Net 30 payment terms",
                "description": "Tests customer priority management workflow pattern"
            },
            {
                "name": "Comprehensive Analysis Query",
                "query": "Perform comprehensive analysis of supply chain stress across all active work orders WO-202506001, WO-202506002, WO-202506003",
                "description": "Tests comprehensive analysis workflow pattern"
            }
        ]
        
        results = []
        
        for i, scenario in enumerate(demo_queries, 1):
            logger.info(f"\n--- Demo Scenario {i}: {scenario['name']} ---")
            logger.info(f"Description: {scenario['description']}")
            logger.info(f"Query: {scenario['query']}")
            
            try:
                # Execute using pattern-based orchestration
                result = await self.orchestration_runner.execute_financial_query(
                    query=scenario['query'],
                    execution_mode="pattern_based"
                )
                
                # Log results
                success = result.get('success', False)
                execution_time = result.get('execution_time', 0)
                workflow_id = result.get('workflow_id', 'unknown')
                
                logger.info(f"Result: {'✓ SUCCESS' if success else '✗ FAILED'}")
                logger.info(f"Execution Time: {execution_time:.2f}s")
                logger.info(f"Workflow ID: {workflow_id}")
                
                if success:
                    query_analysis = result.get('query_analysis', {})
                    logger.info(f"Query Type: {query_analysis.get('type', 'unknown')}")
                    logger.info(f"Confidence: {query_analysis.get('confidence', 0):.2f}")
                    logger.info(f"Workflow Pattern: {query_analysis.get('workflow_pattern', 'unknown')}")
                    
                    # Log agent results
                    if result.get('mysql_analysis'):
                        mysql_success = result['mysql_analysis'].get('success', False)
                        logger.info(f"  MySQL Analysis: {'✓' if mysql_success else '✗'}")
                    
                    if result.get('shortage_analysis'):
                        shortage_success = result['shortage_analysis'].get('success', False)
                        shortage_risk = result['shortage_analysis'].get('risk_level', 'unknown')
                        logger.info(f"  Shortage Analysis: {'✓' if shortage_success else '✗'} (Risk: {shortage_risk})")
                    
                    if result.get('alert_management'):
                        alert_success = result['alert_management'].get('success', False)
                        alerts_sent = len(result['alert_management'].get('alerts_sent', []))
                        logger.info(f"  Alert Management: {'✓' if alert_success else '✗'} ({alerts_sent} alerts)")
                
                else:
                    error = result.get('error', 'Unknown error')
                    logger.error(f"Error: {error}")
                    
                    # Check for clarification requirements
                    if result.get('requires_clarification', False):
                        logger.warning("Query requires clarification:")
                        for clarification in result.get('suggested_clarifications', []):
                            logger.warning(f"  - {clarification}")
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"Demo scenario {i} failed with exception: {e}")
                results.append({"success": False, "error": str(e), "scenario": scenario['name']})
        
        # Summary
        logger.info(f"\n=== Demo Results Summary ===")
        successful_scenarios = sum(1 for r in results if r.get('success', False))
        total_scenarios = len(results)
        
        logger.info(f"Successful Scenarios: {successful_scenarios}/{total_scenarios}")
        
        # Execution statistics
        stats = self.orchestration_runner.get_execution_statistics()
        logger.info(f"Total Workflows Executed: {stats['total_workflows']}")
        logger.info(f"Average Execution Time: {stats['average_execution_time']:.2f}s")
    
    async def interactive_mode(self) -> None:
        """Run interactive query mode."""
        if not self.orchestration_runner:
            logger.error("Orchestration system not initialized")
            return
        
        logger.info("\n=== Interactive Orchestration Mode ===")
        logger.info("Enter financial analysis queries. Type 'quit' to exit.")
        logger.info("Example queries:")
        logger.info("  - Analyze shortage for order CUSTORD-202506001")
        logger.info("  - Check supplier risk for MetaMind Technology")
        logger.info("  - Review customer priorities for Tech Pioneer")
        
        while True:
            try:
                query = input("\nQuery> ").strip()
                
                if query.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not query:
                    continue
                
                logger.info(f"Executing query: {query}")
                
                result = await self.orchestration_runner.execute_financial_query(
                    query=query,
                    execution_mode="pattern_based"
                )
                
                # Display results
                success = result.get('success', False)
                print(f"\nResult: {'SUCCESS' if success else 'FAILED'}")
                
                if success:
                    execution_time = result.get('execution_time', 0)
                    print(f"Execution Time: {execution_time:.2f}s")
                    
                    query_analysis = result.get('query_analysis', {})
                    print(f"Query Type: {query_analysis.get('type', 'unknown')}")
                    print(f"Workflow Pattern: {query_analysis.get('workflow_pattern', 'unknown')}")
                    
                    # Display brief agent results
                    if result.get('mysql_analysis', {}).get('success'):
                        mysql_response = result['mysql_analysis'].get('response', 'No response')
                        print(f"\nMySQL Analysis: {mysql_response[:200]}{'...' if len(mysql_response) > 200 else ''}")
                    
                    if result.get('shortage_analysis', {}).get('success'):
                        shortage_index = result['shortage_analysis'].get('shortage_index', 0)
                        risk_level = result['shortage_analysis'].get('risk_level', 'unknown')
                        print(f"\nShortage Analysis: Index {shortage_index:.3f}, Risk {risk_level}")
                    
                    if result.get('alert_management', {}).get('success'):
                        alerts_count = len(result['alert_management'].get('alerts_sent', []))
                        print(f"\nAlert Management: {alerts_count} alerts processed")
                
                else:
                    error = result.get('error', 'Unknown error')
                    print(f"Error: {error}")
                    
                    if result.get('requires_clarification'):
                        print("\nQuery needs clarification:")
                        for clarification in result.get('suggested_clarifications', []):
                            print(f"  - {clarification}")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"Interactive query failed: {e}")
        
        logger.info("Interactive mode ended")


async def main():
    """Main entry point for orchestrated financial analysis."""
    demo = OrchestrationDemo()
    
    # Initialize system
    if not await demo.initialize_system():
        logger.error("Failed to initialize orchestration system")
        return False
    
    # Run health check
    await demo.run_health_check()
    
    # Check command line arguments
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == "demo":
            await demo.run_demo_scenarios()
        elif mode == "interactive":
            await demo.interactive_mode()
        elif mode == "health":
            # Only health check (already done above)
            pass
        else:
            logger.error(f"Unknown mode: {mode}")
            logger.info("Available modes: demo, interactive, health")
            return False
    else:
        # Default: run demo scenarios
        await demo.run_demo_scenarios()
    
    logger.info("\n=== Orchestrated Financial Analysis Complete ===")
    return True


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\nInterrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)