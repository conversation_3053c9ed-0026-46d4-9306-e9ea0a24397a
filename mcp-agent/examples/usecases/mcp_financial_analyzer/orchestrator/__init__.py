"""
Orchestration Package for MCP Financial Analyzer
===============================================

This package provides intelligent orchestration capabilities for coordinating
multiple financial analysis agents through sophisticated workflow management,
query processing, and context sharing mechanisms.

Key Components:
- financial_orchestrator: Main orchestrator class extending MCP framework
- context_manager: Advanced context management and data sharing
- query_processor: Intelligent query parsing and routing
- workflow_patterns: Predefined workflow templates and execution patterns
- orchestration_runner: Main execution engine and API interface

Usage:
    from orchestrator import create_orchestration_runner
    
    runner = create_orchestration_runner(
        mysql_agent=mysql_agent,
        shortage_agent=shortage_agent,
        alert_agent=alert_agent,
        llm_factory=llm_factory
    )
    
    result = await runner.execute_financial_query("Analyze shortage for order CUSTORD-*********")
"""

from .financial_orchestrator import FinancialOrchestrator, FinancialAnalysisContext
from .context_manager import (
    ContextManager, 
    FinancialWorkflowContext,
    MySQLContextData,
    ShortageContextData,
    AlertContextData
)
from .query_processor import (
    FinancialQueryProcessor,
    QueryType,
    WorkflowPattern as QueryWorkflowPattern,
    ParsedQuery,
    EntityCollection,
    QueryParameters
)
from .workflow_patterns import (
    WorkflowPatternRegistry,
    WorkflowExecutor,
    WorkflowPattern,
    WorkflowStep,
    ExecutionMode
)
from .orchestration_runner import (
    OrchestrationRunner,
    create_orchestration_runner
)
from .exceptions import (
    OrchestrationError,
    QueryProcessingError,
    ContextError,
    AgentExecutionError,
)

__version__ = "1.0.0"
__author__ = "MCP Financial Analyzer Team"

# Package-level exports
__all__ = [
    # Main classes
    "FinancialOrchestrator",
    "ContextManager",
    "FinancialQueryProcessor",
    "WorkflowPatternRegistry",
    "WorkflowExecutor",
    "OrchestrationRunner",

    # Factory functions
    "create_orchestration_runner",

    # Data structures
    "FinancialAnalysisContext",
    "FinancialWorkflowContext",
    "MySQLContextData",
    "ShortageContextData",
    "AlertContextData",
    "ParsedQuery",
    "EntityCollection",
    "QueryParameters",
    "WorkflowPattern",
    "WorkflowStep",

    # Enums
    "QueryType",
    "QueryWorkflowPattern",
    "ExecutionMode",

    # Exceptions
    "OrchestrationError",
    "QueryProcessingError",
    "ContextError",
    "AgentExecutionError",
]

def get_version():
    """Get package version."""
    return __version__

def get_supported_query_types():
    """Get list of supported query types."""
    return [qt.value for qt in QueryType]

def get_available_workflow_patterns():
    """Get available workflow patterns."""
    registry = WorkflowPatternRegistry()
    return list(registry.get_all_patterns().keys())