"""
Configuration Management for Financial Orchestrator
==================================================

Centralized configuration management system for the orchestration components,
providing settings, validation, and environment-specific configurations.

Features:
- Environment-based configuration
- Validation and default values
- Runtime configuration updates
- Component-specific settings
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field, asdict
from pathlib import Path
from enum import Enum

from pydantic import BaseModel, Field, ValidationError, validator

from mcp_agent.logging.logger import get_logger

logger = get_logger(__name__)


class ExecutionMode(Enum):
    """Available execution modes for orchestration."""
    PATTERN_BASED = "pattern_based"
    ORCHESTRATOR_BASED = "orchestrator_based"
    HYBRID = "hybrid"


class LogLevel(Enum):
    """Available logging levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class MCPServerConfig:
    """Configuration for individual MCP servers."""
    name: str
    transport: str = "sse"
    url: str = ""
    host: str = "localhost"
    port: int = 8000
    timeout: int = 120
    retry_count: int = 2
    health_endpoint: str = "/sse"
    
    @property
    def full_url(self) -> str:
        """Get full server URL."""
        if self.url:
            return self.url
        return f"http://{self.host}:{self.port}"
    
    @property
    def health_url(self) -> str:
        """Get health check URL."""
        return f"{self.full_url}{self.health_endpoint}"


@dataclass
class AgentConfig:
    """Configuration for individual agents."""
    name: str
    agent_type: str
    timeout: int = 120
    retry_count: int = 2
    optional: bool = False
    initialization_required: bool = True
    capabilities: List[str] = field(default_factory=list)
    mcp_servers: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        if not self.capabilities:
            # Set default capabilities based on agent type
            capability_map = {
                "mysql_analyzer": ["historical_data_analysis", "supplier_analysis", "customer_queries"],
                "shortage_analyzer": ["shortage_calculation", "risk_assessment", "component_analysis"],
                "alert_manager": ["alert_generation", "notification_delivery", "priority_assessment"]
            }
            self.capabilities = capability_map.get(self.agent_type, [])


@dataclass
class OrchestrationConfig:
    """Configuration for orchestration system."""
    execution_mode: ExecutionMode = ExecutionMode.PATTERN_BASED
    default_workflow_pattern: str = "comprehensive"
    max_concurrent_workflows: int = 10
    context_persistence: bool = True
    context_directory: str = "./workflow_contexts"
    query_confidence_threshold: float = 0.6
    performance_monitoring: bool = True
    enable_caching: bool = True
    cache_ttl_seconds: int = 3600


@dataclass
class PerformanceConfig:
    """Performance-related configuration."""
    query_processing_timeout: int = 30
    workflow_execution_timeout: int = 300
    agent_initialization_timeout: int = 60
    context_save_interval: int = 10
    max_execution_history: int = 1000
    performance_metrics_retention_days: int = 7


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: LogLevel = LogLevel.INFO
    format: str = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
    file_logging: bool = True
    log_directory: str = "./logs"
    log_rotation: bool = True
    max_log_files: int = 5
    max_log_size_mb: int = 10
    component_log_levels: Dict[str, LogLevel] = field(default_factory=dict)


class FinancialOrchestratorConfig(BaseModel):
    """Complete configuration for financial orchestrator system."""
    
    # MCP server configurations
    mcp_servers: Dict[str, MCPServerConfig] = Field(default_factory=dict)
    
    # Agent configurations  
    agents: Dict[str, AgentConfig] = Field(default_factory=dict)
    
    # Orchestration settings
    orchestration: OrchestrationConfig = Field(default_factory=OrchestrationConfig)
    
    # Performance settings
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    
    # Logging settings
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    
    # Environment and metadata
    environment: str = Field(default="development", description="Environment name")
    version: str = Field(default="1.0.0", description="Configuration version")
    
    class Config:
        arbitrary_types_allowed = True
        use_enum_values = True
    
    @validator('orchestration')
    def validate_orchestration(cls, v):
        """Validate orchestration configuration."""
        if v.query_confidence_threshold < 0.0 or v.query_confidence_threshold > 1.0:
            raise ValueError("query_confidence_threshold must be between 0.0 and 1.0")
        
        if v.max_concurrent_workflows < 1:
            raise ValueError("max_concurrent_workflows must be at least 1")
        
        return v
    
    @validator('performance')
    def validate_performance(cls, v):
        """Validate performance configuration."""
        if v.query_processing_timeout <= 0:
            raise ValueError("query_processing_timeout must be positive")
        
        if v.workflow_execution_timeout <= 0:
            raise ValueError("workflow_execution_timeout must be positive")
        
        return v


class ConfigurationManager:
    """Manager for orchestration system configuration."""
    
    def __init__(self, config_path: Optional[Path] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_path = config_path or Path("orchestrator_config.yaml")
        self._config: Optional[FinancialOrchestratorConfig] = None
        self._environment_overrides: Dict[str, Any] = {}
        
        logger.info(f"ConfigurationManager initialized with config path: {self.config_path}")
    
    def load_config(self, config_path: Optional[Path] = None) -> FinancialOrchestratorConfig:
        """
        Load configuration from file and environment variables.
        
        Args:
            config_path: Optional path to configuration file
            
        Returns:
            Loaded configuration object
        """
        if config_path:
            self.config_path = config_path
        
        try:
            # Load base configuration
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    config_data = yaml.safe_load(f) or {}
                logger.info(f"Loaded configuration from {self.config_path}")
            else:
                logger.warning(f"Configuration file {self.config_path} not found, using defaults")
                config_data = {}
            
            # Apply environment overrides
            config_data = self._apply_environment_overrides(config_data)
            
            # Create configuration object with defaults
            config_data = self._ensure_default_config(config_data)
            
            # Validate and create configuration
            self._config = FinancialOrchestratorConfig(**config_data)
            
            logger.info(f"Configuration loaded successfully for environment: {self._config.environment}")
            return self._config
            
        except ValidationError as e:
            logger.error(f"Configuration validation failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            # Return default configuration
            self._config = self._create_default_config()
            return self._config
    
    def _apply_environment_overrides(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment variable overrides to configuration."""
        
        # Environment variable mappings
        env_mappings = {
            "ORCHESTRATOR_ENVIRONMENT": ("environment",),
            "ORCHESTRATOR_LOG_LEVEL": ("logging", "level"),
            "ORCHESTRATOR_CONTEXT_DIR": ("orchestration", "context_directory"),
            "ORCHESTRATOR_MAX_WORKFLOWS": ("orchestration", "max_concurrent_workflows"),
            "ORCHESTRATOR_ENABLE_PERSISTENCE": ("orchestration", "context_persistence"),
            "MYSQL_SERVER_HOST": ("mcp_servers", "mysql", "host"),
            "MYSQL_SERVER_PORT": ("mcp_servers", "mysql", "port"),
            "SHORTAGE_SERVER_HOST": ("mcp_servers", "shortage-index", "host"),
            "SHORTAGE_SERVER_PORT": ("mcp_servers", "shortage-index", "port"),
            "ALERT_SERVER_HOST": ("mcp_servers", "alert-notification", "host"),
            "ALERT_SERVER_PORT": ("mcp_servers", "alert-notification", "port"),
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # Navigate to the config location and set value
                current = config_data
                for key in config_path[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]
                
                # Convert value to appropriate type
                final_key = config_path[-1]
                if final_key.endswith("_port") or final_key in ["max_concurrent_workflows"]:
                    current[final_key] = int(env_value)
                elif final_key in ["context_persistence", "performance_monitoring", "enable_caching"]:
                    current[final_key] = env_value.lower() in ("true", "1", "yes", "on")
                else:
                    current[final_key] = env_value
                
                logger.info(f"Applied environment override: {env_var}={env_value}")
        
        return config_data
    
    def _ensure_default_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Ensure configuration has all required default values."""
        
        # Default MCP servers
        if "mcp_servers" not in config_data:
            config_data["mcp_servers"] = {}
        
        default_servers = {
            "mysql": MCPServerConfig(
                name="mysql",
                host="localhost",
                port=8702,
                transport="sse",
                health_endpoint="/sse"
            ),
            "shortage-index": MCPServerConfig(
                name="shortage-index",
                host="localhost", 
                port=6970,
                transport="sse",
                health_endpoint="/sse"
            ),
            "alert-notification": MCPServerConfig(
                name="alert-notification",
                host="localhost",
                port=6972,
                transport="sse",
                health_endpoint="/sse"
            )
        }
        
        for server_name, server_config in default_servers.items():
            if server_name not in config_data["mcp_servers"]:
                config_data["mcp_servers"][server_name] = asdict(server_config)
        
        # Default agents
        if "agents" not in config_data:
            config_data["agents"] = {}
        
        default_agents = {
            "mysql_analyzer": AgentConfig(
                name="mysql_analyzer",
                agent_type="mysql_analyzer",
                mcp_servers=["mysql"],
                timeout=120
            ),
            "shortage_analyzer": AgentConfig(
                name="shortage_analyzer",
                agent_type="shortage_analyzer",
                mcp_servers=["shortage-index"],
                timeout=90
            ),
            "alert_manager": AgentConfig(
                name="alert_manager",
                agent_type="alert_manager",
                mcp_servers=["alert-notification"],
                timeout=60,
                optional=True
            )
        }
        
        for agent_name, agent_config in default_agents.items():
            if agent_name not in config_data["agents"]:
                config_data["agents"][agent_name] = asdict(agent_config)
        
        return config_data
    
    def _create_default_config(self) -> FinancialOrchestratorConfig:
        """Create default configuration when loading fails."""
        logger.info("Creating default configuration")
        
        return FinancialOrchestratorConfig(
            mcp_servers={
                "mysql": MCPServerConfig(name="mysql", port=8702),
                "shortage-index": MCPServerConfig(name="shortage-index", port=6970),
                "alert-notification": MCPServerConfig(name="alert-notification", port=6972),
            },
            agents={
                "mysql_analyzer": AgentConfig(name="mysql_analyzer", agent_type="mysql_analyzer"),
                "shortage_analyzer": AgentConfig(name="shortage_analyzer", agent_type="shortage_analyzer"),
                "alert_manager": AgentConfig(name="alert_manager", agent_type="alert_manager", optional=True),
            }
        )
    
    def save_config(self, config_path: Optional[Path] = None) -> bool:
        """
        Save current configuration to file.
        
        Args:
            config_path: Optional path to save configuration
            
        Returns:
            True if successful, False otherwise
        """
        if not self._config:
            logger.error("No configuration to save")
            return False
        
        save_path = config_path or self.config_path
        
        try:
            # Convert to dictionary for YAML serialization
            config_dict = self._config.dict()
            
            # Convert dataclass objects to dictionaries
            for server_name, server_config in config_dict["mcp_servers"].items():
                if hasattr(server_config, '__dict__'):
                    config_dict["mcp_servers"][server_name] = asdict(server_config)
            
            for agent_name, agent_config in config_dict["agents"].items():
                if hasattr(agent_config, '__dict__'):
                    config_dict["agents"][agent_name] = asdict(agent_config)
            
            # Ensure directory exists
            save_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save to YAML file
            with open(save_path, 'w') as f:
                yaml.dump(config_dict, f, default_flow_style=False, indent=2)
            
            logger.info(f"Configuration saved to {save_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False
    
    def get_config(self) -> Optional[FinancialOrchestratorConfig]:
        """Get current configuration."""
        return self._config
    
    def update_config(self, updates: Dict[str, Any]) -> bool:
        """
        Update configuration with new values.
        
        Args:
            updates: Dictionary of configuration updates
            
        Returns:
            True if successful, False otherwise
        """
        if not self._config:
            logger.error("No configuration loaded")
            return False
        
        try:
            # Create new configuration with updates
            current_dict = self._config.dict()
            
            # Apply updates recursively
            def apply_updates(current: Dict[str, Any], updates: Dict[str, Any]):
                for key, value in updates.items():
                    if isinstance(value, dict) and key in current and isinstance(current[key], dict):
                        apply_updates(current[key], value)
                    else:
                        current[key] = value
            
            apply_updates(current_dict, updates)
            
            # Validate updated configuration
            self._config = FinancialOrchestratorConfig(**current_dict)
            
            logger.info("Configuration updated successfully")
            return True
            
        except ValidationError as e:
            logger.error(f"Configuration update validation failed: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to update configuration: {e}")
            return False
    
    def get_mcp_server_config(self, server_name: str) -> Optional[MCPServerConfig]:
        """Get configuration for specific MCP server."""
        if not self._config or server_name not in self._config.mcp_servers:
            return None
        
        server_data = self._config.mcp_servers[server_name]
        if isinstance(server_data, MCPServerConfig):
            return server_data
        else:
            return MCPServerConfig(**server_data)
    
    def get_agent_config(self, agent_name: str) -> Optional[AgentConfig]:
        """Get configuration for specific agent."""
        if not self._config or agent_name not in self._config.agents:
            return None
        
        agent_data = self._config.agents[agent_name]
        if isinstance(agent_data, AgentConfig):
            return agent_data
        else:
            return AgentConfig(**agent_data)
    
    def validate_connectivity(self) -> Dict[str, bool]:
        """Validate that all configured services are reachable."""
        if not self._config:
            return {"error": "No configuration loaded"}
        
        connectivity = {}
        
        # Check MCP servers
        import aiohttp
        import asyncio
        
        async def check_server(server_name: str, server_config: MCPServerConfig) -> bool:
            try:
                timeout = aiohttp.ClientTimeout(total=5)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(server_config.health_url) as response:
                        return True
            except Exception:
                return False
        
        async def check_all_servers():
            tasks = []
            for server_name, server_data in self._config.mcp_servers.items():
                server_config = server_data if isinstance(server_data, MCPServerConfig) else MCPServerConfig(**server_data)
                tasks.append((server_name, check_server(server_name, server_config)))
            
            for server_name, task in tasks:
                try:
                    connectivity[f"mcp_server_{server_name}"] = await task
                except Exception:
                    connectivity[f"mcp_server_{server_name}"] = False
        
        # Run connectivity check
        try:
            loop = asyncio.get_event_loop()
            loop.run_until_complete(check_all_servers())
        except Exception as e:
            logger.error(f"Connectivity check failed: {e}")
            connectivity["connectivity_check_error"] = str(e)
        
        return connectivity


# Global configuration manager instance
_config_manager: Optional[ConfigurationManager] = None

def get_config_manager(config_path: Optional[Path] = None) -> ConfigurationManager:
    """Get global configuration manager instance."""
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ConfigurationManager(config_path)
    
    return _config_manager

def load_orchestration_config(config_path: Optional[Path] = None) -> FinancialOrchestratorConfig:
    """Load orchestration configuration."""
    manager = get_config_manager(config_path)
    return manager.load_config()

def get_orchestration_config() -> Optional[FinancialOrchestratorConfig]:
    """Get current orchestration configuration."""
    manager = get_config_manager()
    return manager.get_config()