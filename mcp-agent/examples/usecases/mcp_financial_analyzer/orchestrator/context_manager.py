"""
Context Management System for Financial Orchestrator
==================================================

Advanced context management system that handles data sharing, persistence,
and validation across the three financial analysis agents.

Key Features:
- Hierarchical context structure extending MCP framework patterns
- Data transformation and validation between agents
- Context persistence and recovery mechanisms
- Agent-specific data isolation with shared context access
"""

import json
import logging
from typing import Dict, List, Any, Optional, Type, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime
from pathlib import Path

from pydantic import BaseModel, ValidationError, Field
from mcp_agent.workflows.orchestrator.orchestrator_models import PlanResult, StepResult, TaskWithResult
from mcp_agent.logging.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ContextMetadata:
    """Metadata about the financial analysis context."""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    version: str = "1.0"
    workflow_id: str = ""
    query_hash: str = ""
    agent_executions: List[str] = field(default_factory=list)


class MySQLContextData(BaseModel):
    """Structured data from MySQL agent analysis."""
    query: str = Field(description="Original SQL query or analysis request")
    response: str = Field(description="MySQL agent response text")
    reasoning: str = Field(default="", description="Agent reasoning process")
    table_data: Optional[Dict[str, Any]] = Field(default=None, description="Structured table data")
    entities_found: Dict[str, List[str]] = Field(default_factory=dict, description="Extracted entities")
    success: bool = Field(default=True, description="Whether analysis succeeded")
    execution_time: float = Field(default=0.0, description="Execution time in seconds")
    error: Optional[str] = Field(default=None, description="Error message if failed")


class ShortageContextData(BaseModel):
    """Structured data from shortage analyzer agent."""
    company_name: str = Field(description="Company being analyzed")
    shortage_index: float = Field(description="Calculated shortage index (0.0-1.0)")
    risk_level: str = Field(description="Risk classification (LOW/MEDIUM/HIGH)")
    weighted_shortage_index: Optional[float] = Field(default=None, description="Weighted shortage calculation")
    components_analyzed: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Component breakdown")
    recommendations: List[str] = Field(default_factory=list, description="Analysis recommendations")
    response: str = Field(description="Full analysis response text")
    mcp_service_status: str = Field(default="unknown", description="MCP service availability")
    calculation_method: str = Field(default="fallback", description="Calculation method used")
    success: bool = Field(default=True, description="Whether analysis succeeded")
    execution_time: float = Field(default=0.0, description="Execution time in seconds")
    error: Optional[str] = Field(default=None, description="Error message if failed")


class AlertContextData(BaseModel):
    """Structured data from alert manager agent."""
    company_name: str = Field(description="Company receiving alerts")
    alerts_sent: List[str] = Field(default_factory=list, description="List of alerts sent")
    notification_results: List[str] = Field(default_factory=list, description="Notification delivery results")
    alert_summary: str = Field(description="Summary of alert processing")
    channels_used: List[str] = Field(default_factory=list, description="Communication channels used")
    severity_level: str = Field(default="medium", description="Alert severity level")
    success: bool = Field(default=True, description="Whether alert processing succeeded")
    execution_time: float = Field(default=0.0, description="Execution time in seconds")
    error: Optional[str] = Field(default=None, description="Error message if failed")


class FinancialWorkflowContext(BaseModel):
    """Complete context data for a financial analysis workflow."""
    workflow_id: str = Field(description="Unique workflow identifier")
    original_query: str = Field(description="Original user query")
    query_type: str = Field(description="Classified query type")
    workflow_pattern: str = Field(description="Selected workflow pattern")
    
    # Agent-specific context data
    mysql_context: Optional[MySQLContextData] = Field(default=None, description="MySQL agent results")
    shortage_context: Optional[ShortageContextData] = Field(default=None, description="Shortage agent results")
    alert_context: Optional[AlertContextData] = Field(default=None, description="Alert agent results")
    
    # Workflow metadata
    metadata: ContextMetadata = Field(default_factory=ContextMetadata, description="Context metadata")
    
    # Execution tracking
    current_step: int = Field(default=0, description="Current workflow step")
    total_steps: int = Field(default=3, description="Total workflow steps")
    is_complete: bool = Field(default=False, description="Whether workflow is complete")
    
    # Error handling
    errors: List[str] = Field(default_factory=list, description="Workflow errors encountered")
    warnings: List[str] = Field(default_factory=list, description="Workflow warnings")


class ContextManager:
    """Advanced context manager for financial analysis workflows."""
    
    def __init__(self, persist_context: bool = True, context_dir: Optional[Path] = None):
        """
        Initialize the context manager.
        
        Args:
            persist_context: Whether to persist context to disk
            context_dir: Directory to store context files
        """
        self.persist_context = persist_context
        self.context_dir = context_dir or Path("./workflow_contexts")
        
        if self.persist_context:
            self.context_dir.mkdir(exist_ok=True)
        
        # Active contexts
        self.active_contexts: Dict[str, FinancialWorkflowContext] = {}
        
        logger.info(f"ContextManager initialized with persistence: {persist_context}")
    
    def create_context(
        self,
        workflow_id: str,
        query: str,
        query_type: str,
        workflow_pattern: str
    ) -> FinancialWorkflowContext:
        """Create a new workflow context."""
        context = FinancialWorkflowContext(
            workflow_id=workflow_id,
            original_query=query,
            query_type=query_type,
            workflow_pattern=workflow_pattern,
            metadata=ContextMetadata(
                workflow_id=workflow_id,
                query_hash=self._hash_query(query)
            )
        )
        
        self.active_contexts[workflow_id] = context
        
        if self.persist_context:
            self._save_context(context)
        
        logger.info(f"Created new context for workflow {workflow_id}")
        return context
    
    def update_mysql_context(
        self,
        workflow_id: str,
        mysql_data: Dict[str, Any],
        execution_time: float = 0.0
    ) -> None:
        """Update context with MySQL agent results."""
        if workflow_id not in self.active_contexts:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        context = self.active_contexts[workflow_id]
        
        try:
            # Create structured MySQL context data
            mysql_context = MySQLContextData(
                query=mysql_data.get("query", context.original_query),
                response=mysql_data.get("response", ""),
                reasoning=mysql_data.get("reasoning", ""),
                success=mysql_data.get("success", True),
                execution_time=execution_time,
                error=mysql_data.get("error")
            )
            
            # Extract structured data if available
            if "entities" in mysql_data:
                mysql_context.entities_found = mysql_data["entities"]
            
            context.mysql_context = mysql_context
            context.current_step = max(context.current_step, 1)
            context.metadata.updated_at = datetime.now()
            context.metadata.agent_executions.append(f"mysql:{datetime.now().isoformat()}")
            
            if self.persist_context:
                self._save_context(context)
                
            logger.info(f"Updated MySQL context for workflow {workflow_id}")
            
        except ValidationError as e:
            error_msg = f"MySQL context validation failed: {e}"
            logger.error(error_msg)
            context.errors.append(error_msg)
    
    def update_shortage_context(
        self,
        workflow_id: str,
        shortage_data: Dict[str, Any],
        execution_time: float = 0.0
    ) -> None:
        """Update context with shortage analyzer results."""
        if workflow_id not in self.active_contexts:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        context = self.active_contexts[workflow_id]
        
        try:
            # Create structured shortage context data
            shortage_context = ShortageContextData(
                company_name=shortage_data.get("company_name", "Unknown"),
                shortage_index=shortage_data.get("shortage_index", 0.0),
                risk_level=shortage_data.get("risk_level", "UNKNOWN"),
                weighted_shortage_index=shortage_data.get("weighted_shortage_index"),
                response=shortage_data.get("response", ""),
                success=shortage_data.get("success", True),
                execution_time=execution_time,
                error=shortage_data.get("error")
            )
            
            # Extract additional structured data
            if "recommendations" in shortage_data:
                shortage_context.recommendations = shortage_data["recommendations"]
            if "mcp_service_status" in shortage_data:
                shortage_context.mcp_service_status = shortage_data["mcp_service_status"]
            if "calculation_method" in shortage_data:
                shortage_context.calculation_method = shortage_data["calculation_method"]
            
            context.shortage_context = shortage_context
            context.current_step = max(context.current_step, 2)
            context.metadata.updated_at = datetime.now()
            context.metadata.agent_executions.append(f"shortage:{datetime.now().isoformat()}")
            
            if self.persist_context:
                self._save_context(context)
                
            logger.info(f"Updated shortage context for workflow {workflow_id}")
            
        except ValidationError as e:
            error_msg = f"Shortage context validation failed: {e}"
            logger.error(error_msg)
            context.errors.append(error_msg)
    
    def update_alert_context(
        self,
        workflow_id: str,
        alert_data: Dict[str, Any],
        execution_time: float = 0.0
    ) -> None:
        """Update context with alert manager results."""
        if workflow_id not in self.active_contexts:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        context = self.active_contexts[workflow_id]
        
        try:
            # Create structured alert context data
            alert_context = AlertContextData(
                company_name=alert_data.get("company_name", "Unknown"),
                alerts_sent=alert_data.get("alerts_sent", []),
                notification_results=alert_data.get("notification_results", []),
                alert_summary=alert_data.get("alert_summary", ""),
                success=alert_data.get("success", True),
                execution_time=execution_time,
                error=alert_data.get("error")
            )
            
            context.alert_context = alert_context
            context.current_step = max(context.current_step, 3)
            context.is_complete = True
            context.metadata.updated_at = datetime.now()
            context.metadata.agent_executions.append(f"alert:{datetime.now().isoformat()}")
            
            if self.persist_context:
                self._save_context(context)
                
            logger.info(f"Updated alert context for workflow {workflow_id}")
            
        except ValidationError as e:
            error_msg = f"Alert context validation failed: {e}"
            logger.error(error_msg)
            context.errors.append(error_msg)
    
    def get_context_for_agent(
        self,
        workflow_id: str,
        agent_type: str,
        include_full_history: bool = True
    ) -> str:
        """Get formatted context data for a specific agent."""
        if workflow_id not in self.active_contexts:
            return "No workflow context available."
        
        context = self.active_contexts[workflow_id]
        context_parts = []
        
        # Add workflow overview
        context_parts.append(f"Workflow Analysis: {context.query_type} ({context.workflow_pattern})")
        context_parts.append(f"Original Query: {context.original_query}")
        context_parts.append(f"Progress: Step {context.current_step}/{context.total_steps}")
        
        # Add relevant previous results based on agent type
        if agent_type == "shortage_analyzer" and context.mysql_context:
            context_parts.append(f"\nMySQL Analysis Results:")
            context_parts.append(f"- Response: {context.mysql_context.response}")
            if context.mysql_context.entities_found:
                context_parts.append(f"- Entities Found: {context.mysql_context.entities_found}")
        
        elif agent_type == "alert_manager":
            if context.mysql_context:
                context_parts.append(f"\nMySQL Analysis: {context.mysql_context.response}")
            if context.shortage_context:
                context_parts.append(f"\nShortage Analysis:")
                context_parts.append(f"- Index: {context.shortage_context.shortage_index:.3f}")
                context_parts.append(f"- Risk: {context.shortage_context.risk_level}")
                context_parts.append(f"- Summary: {context.shortage_context.response[:200]}...")
        
        # Add error context if relevant
        if context.errors:
            context_parts.append(f"\nWorkflow Errors: {'; '.join(context.errors)}")
        if context.warnings:
            context_parts.append(f"\nWorkflow Warnings: {'; '.join(context.warnings)}")
        
        return "\n".join(context_parts)
    
    def get_full_context_summary(self, workflow_id: str) -> Dict[str, Any]:
        """Get complete context summary for final reporting."""
        if workflow_id not in self.active_contexts:
            return {"error": "Workflow not found"}
        
        context = self.active_contexts[workflow_id]
        
        summary = {
            "workflow_id": workflow_id,
            "query": context.original_query,
            "type": context.query_type,
            "pattern": context.workflow_pattern,
            "status": "completed" if context.is_complete else "in_progress",
            "steps_completed": context.current_step,
            "total_steps": context.total_steps,
            "created_at": context.metadata.created_at.isoformat(),
            "updated_at": context.metadata.updated_at.isoformat(),
            "agents_executed": len(context.metadata.agent_executions),
        }
        
        # Add agent results summaries
        if context.mysql_context:
            summary["mysql_success"] = context.mysql_context.success
            summary["mysql_response_length"] = len(context.mysql_context.response)
        
        if context.shortage_context:
            summary["shortage_success"] = context.shortage_context.success
            summary["shortage_index"] = context.shortage_context.shortage_index
            summary["risk_level"] = context.shortage_context.risk_level
        
        if context.alert_context:
            summary["alert_success"] = context.alert_context.success
            summary["alerts_sent"] = len(context.alert_context.alerts_sent)
        
        # Add error summary
        summary["errors_count"] = len(context.errors)
        summary["warnings_count"] = len(context.warnings)
        
        return summary
    
    def cleanup_context(self, workflow_id: str) -> None:
        """Clean up context after workflow completion."""
        if workflow_id in self.active_contexts:
            context = self.active_contexts[workflow_id]
            
            # Final save before cleanup
            if self.persist_context:
                self._save_context(context)
            
            # Remove from active contexts
            del self.active_contexts[workflow_id]
            
            logger.info(f"Cleaned up context for workflow {workflow_id}")
    
    def _hash_query(self, query: str) -> str:
        """Create a hash of the query for identification."""
        import hashlib
        return hashlib.md5(query.encode()).hexdigest()[:8]
    
    def _save_context(self, context: FinancialWorkflowContext) -> None:
        """Save context to disk."""
        try:
            context_file = self.context_dir / f"{context.workflow_id}.json"
            
            # Convert to dict for JSON serialization
            context_dict = context.model_dump()
            
            # Handle datetime serialization
            if "metadata" in context_dict:
                metadata = context_dict["metadata"]
                if "created_at" in metadata:
                    metadata["created_at"] = context.metadata.created_at.isoformat()
                if "updated_at" in metadata:
                    metadata["updated_at"] = context.metadata.updated_at.isoformat()
            
            with open(context_file, 'w') as f:
                json.dump(context_dict, f, indent=2)
                
            logger.debug(f"Saved context to {context_file}")
            
        except Exception as e:
            logger.error(f"Failed to save context: {e}")
    
    def load_context(self, workflow_id: str) -> Optional[FinancialWorkflowContext]:
        """Load context from disk."""
        try:
            context_file = self.context_dir / f"{workflow_id}.json"
            
            if not context_file.exists():
                return None
            
            with open(context_file, 'r') as f:
                context_dict = json.load(f)
            
            # Handle datetime deserialization
            if "metadata" in context_dict:
                metadata = context_dict["metadata"]
                if "created_at" in metadata:
                    metadata["created_at"] = datetime.fromisoformat(metadata["created_at"])
                if "updated_at" in metadata:
                    metadata["updated_at"] = datetime.fromisoformat(metadata["updated_at"])
            
            context = FinancialWorkflowContext(**context_dict)
            self.active_contexts[workflow_id] = context
            
            logger.info(f"Loaded context for workflow {workflow_id}")
            return context
            
        except Exception as e:
            logger.error(f"Failed to load context: {e}")
            return None