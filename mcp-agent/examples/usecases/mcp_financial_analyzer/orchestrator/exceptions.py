"""
Custom exception classes for MCP Financial Analyzer Orchestration
-----------------------------------------------------------------
Implements API-specified exceptions to provide structured error handling
throughout the orchestration system.
"""
from __future__ import annotations

from typing import Optional


class OrchestrationError(Exception):
    """Base exception for orchestration system errors."""

    def __init__(
        self,
        message: str,
        workflow_id: Optional[str] = None,
        error_code: Optional[str] = None,
    ) -> None:
        super().__init__(message)
        self.message = message
        self.workflow_id = workflow_id
        self.error_code = error_code

    def __str__(self) -> str:  # pragma: no cover
        base = self.message
        details = []
        if self.workflow_id:
            details.append(f"workflow_id={self.workflow_id}")
        if self.error_code:
            details.append(f"error_code={self.error_code}")
        if details:
            return f"{base} ({', '.join(details)})"
        return base


class QueryProcessingError(OrchestrationError):
    """Exception for query processing failures."""

    def __init__(
        self,
        message: str,
        query: Optional[str] = None,
        confidence: Optional[float] = None,
        workflow_id: Optional[str] = None,
        error_code: Optional[str] = None,
    ) -> None:
        super().__init__(message=message, workflow_id=workflow_id, error_code=error_code)
        self.query = query
        self.confidence = confidence


class ContextError(OrchestrationError):
    """Exception for context management failures."""

    def __init__(
        self,
        message: str,
        context_type: Optional[str] = None,
        workflow_id: Optional[str] = None,
        error_code: Optional[str] = None,
    ) -> None:
        super().__init__(message=message, workflow_id=workflow_id, error_code=error_code)
        self.context_type = context_type


class AgentExecutionError(OrchestrationError):
    """Exception for agent execution failures."""

    def __init__(
        self,
        message: str,
        agent_name: Optional[str] = None,
        step_id: Optional[str] = None,
        workflow_id: Optional[str] = None,
        error_code: Optional[str] = None,
    ) -> None:
        super().__init__(message=message, workflow_id=workflow_id, error_code=error_code)
        self.agent_name = agent_name
        self.step_id = step_id


class NetworkTimeoutError(OrchestrationError):
    """Exception for network timeout failures."""

    def __init__(
        self,
        message: str,
        timeout_seconds: Optional[float] = None,
        service_url: Optional[str] = None,
        workflow_id: Optional[str] = None,
        error_code: Optional[str] = None,
    ) -> None:
        super().__init__(message=message, workflow_id=workflow_id, error_code=error_code)
        self.timeout_seconds = timeout_seconds
        self.service_url = service_url


class ServiceUnavailableError(OrchestrationError):
    """Exception for service unavailability failures."""

    def __init__(
        self,
        message: str,
        service_name: Optional[str] = None,
        service_url: Optional[str] = None,
        workflow_id: Optional[str] = None,
        error_code: Optional[str] = None,
    ) -> None:
        super().__init__(message=message, workflow_id=workflow_id, error_code=error_code)
        self.service_name = service_name
        self.service_url = service_url

