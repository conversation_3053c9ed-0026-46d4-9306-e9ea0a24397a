#!/usr/bin/env python3
"""
Comprehensive End-to-End Test Execution Script
==============================================

This script executes the comprehensive end-to-end tests for the financial analyzer
orchestration workflow using Test-Driven Development (TDD) principles.

Features:
- Automated test discovery and execution
- Real MCP service health checks
- Performance monitoring and reporting
- Test result analysis and documentation
- Issue identification and resolution tracking

Usage:
    python run_comprehensive_e2e_tests.py [options]

Options:
    --real-services    Run tests with real MCP services
    --performance      Include performance tests
    --tdd-only         Run only TDD tests
    --generate-report  Generate comprehensive test report
    --verbose          Enable verbose output
"""

import asyncio
import argparse
import json
import logging
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('e2e_test_execution.log')
    ]
)
logger = logging.getLogger(__name__)


class ComprehensiveTestRunner:
    """Comprehensive test runner for orchestration E2E tests."""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.test_results = {}
        self.performance_metrics = {}
        self.issues_found = []
        self.test_categories = {
            "complete_workflow": "tests/test_comprehensive_e2e_orchestration.py::TestCompleteWorkflowPipeline",
            "context_sharing": "tests/test_comprehensive_e2e_orchestration.py::TestContextSharingValidation", 
            "real_data_integration": "tests/test_comprehensive_e2e_orchestration.py::TestRealDataIntegration",
            "orchestration_validation": "tests/test_comprehensive_e2e_orchestration.py::TestOrchestrationValidation",
            "performance_load": "tests/test_comprehensive_e2e_orchestration.py::TestPerformanceAndLoad",
            "tdd_workflow_patterns": "tests/test_tdd_workflow_patterns.py::TestTDDWorkflowPatternDefinition",
            "tdd_execution_logic": "tests/test_tdd_workflow_patterns.py::TestTDDPatternExecutionLogic"
        }
    
    async def check_service_health(self) -> Dict[str, bool]:
        """Check health of required MCP services."""
        logger.info("Checking MCP service health...")
        
        services = {
            "mysql": "http://localhost:8702/sse",
            "shortage": "http://localhost:6970/sse", 
            "alert": "http://localhost:6972/sse"
        }
        
        health_status = {}
        
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            for service_name, url in services.items():
                try:
                    async with session.get(url, timeout=5) as response:
                        health_status[service_name] = response.status in [200, 404, 405]
                        logger.info(f"Service {service_name}: {'✓' if health_status[service_name] else '✗'}")
                except Exception as e:
                    health_status[service_name] = False
                    logger.warning(f"Service {service_name} health check failed: {e}")
        
        return health_status
    
    def run_test_category(
        self, 
        category: str, 
        test_path: str, 
        markers: List[str] = None,
        capture_performance: bool = False
    ) -> Dict[str, Any]:
        """Run a specific test category."""
        logger.info(f"Running test category: {category}")
        
        # Build pytest command
        cmd = ["python", "-m", "pytest", test_path, "-v", "--tb=short"]
        
        # Add markers
        if markers:
            for marker in markers:
                cmd.extend(["-m", marker])
        
        # Add performance monitoring
        if capture_performance:
            cmd.extend(["--benchmark-only", "--benchmark-json=benchmark_results.json"])
        
        # Add real service marker if needed
        if "real_data" in category or "integration" in category:
            cmd.extend(["-m", "real_service"])
        
        start_time = time.time()
        
        try:
            # Run tests
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout per category
            )
            
            execution_time = time.time() - start_time
            
            # Parse results
            test_result = {
                "category": category,
                "success": result.returncode == 0,
                "execution_time": execution_time,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode
            }
            
            # Extract test statistics
            if "passed" in result.stdout or "failed" in result.stdout:
                test_result["statistics"] = self._parse_test_statistics(result.stdout)
            
            # Identify issues
            if not test_result["success"]:
                issues = self._extract_issues(result.stdout, result.stderr)
                test_result["issues"] = issues
                self.issues_found.extend(issues)
            
            logger.info(f"Category {category}: {'✓ PASSED' if test_result['success'] else '✗ FAILED'} "
                       f"({execution_time:.2f}s)")
            
            return test_result
            
        except subprocess.TimeoutExpired:
            logger.error(f"Test category {category} timed out")
            return {
                "category": category,
                "success": False,
                "execution_time": 300,
                "error": "Test execution timed out",
                "issues": [{"type": "timeout", "message": "Test execution exceeded 5 minute limit"}]
            }
        except Exception as e:
            logger.error(f"Error running test category {category}: {e}")
            return {
                "category": category,
                "success": False,
                "execution_time": time.time() - start_time,
                "error": str(e),
                "issues": [{"type": "execution_error", "message": str(e)}]
            }
    
    def _parse_test_statistics(self, output: str) -> Dict[str, int]:
        """Parse test statistics from pytest output."""
        stats = {"passed": 0, "failed": 0, "skipped": 0, "errors": 0}
        
        # Look for pytest summary line
        lines = output.split('\n')
        for line in lines:
            if "passed" in line or "failed" in line:
                # Extract numbers
                import re
                numbers = re.findall(r'(\d+)\s+(passed|failed|skipped|error)', line)
                for count, status in numbers:
                    if status in stats:
                        stats[status] = int(count)
        
        return stats
    
    def _extract_issues(self, stdout: str, stderr: str) -> List[Dict[str, str]]:
        """Extract issues from test output."""
        issues = []
        
        # Common failure patterns
        failure_patterns = [
            (r"FAILED.*::(.*)", "test_failure"),
            (r"ERROR.*::(.*)", "test_error"),
            (r"AssertionError: (.*)", "assertion_error"),
            (r"TimeoutError: (.*)", "timeout_error"),
            (r"ConnectionError: (.*)", "connection_error"),
            (r"ImportError: (.*)", "import_error")
        ]
        
        import re
        
        combined_output = stdout + "\n" + stderr
        
        for pattern, issue_type in failure_patterns:
            matches = re.findall(pattern, combined_output, re.MULTILINE)
            for match in matches:
                issues.append({
                    "type": issue_type,
                    "message": match.strip(),
                    "source": "test_execution"
                })
        
        return issues
    
    async def run_comprehensive_tests(
        self,
        include_real_services: bool = False,
        include_performance: bool = False,
        tdd_only: bool = False
    ) -> Dict[str, Any]:
        """Run comprehensive end-to-end tests."""
        logger.info("Starting comprehensive E2E test execution")
        
        # Check service health if running real service tests
        service_health = {}
        if include_real_services:
            service_health = await self.check_service_health()
            
            # Skip real service tests if services are not available
            if not all(service_health.values()):
                logger.warning("Some services are not available, skipping real service tests")
                include_real_services = False
        
        # Determine which test categories to run
        categories_to_run = []
        
        if tdd_only:
            categories_to_run = ["tdd_workflow_patterns", "tdd_execution_logic"]
        else:
            categories_to_run = [
                "complete_workflow",
                "context_sharing",
                "orchestration_validation",
                "tdd_workflow_patterns",
                "tdd_execution_logic"
            ]
            
            if include_real_services:
                categories_to_run.append("real_data_integration")
            
            if include_performance:
                categories_to_run.append("performance_load")
        
        # Run test categories
        for category in categories_to_run:
            if category in self.test_categories:
                test_path = self.test_categories[category]
                
                # Determine markers
                markers = []
                if category == "tdd_workflow_patterns" or category == "tdd_execution_logic":
                    markers.append("tdd")
                if category == "real_data_integration":
                    markers.append("real_service")
                if category == "performance_load":
                    markers.append("performance")
                
                # Run tests
                result = self.run_test_category(
                    category=category,
                    test_path=test_path,
                    markers=markers,
                    capture_performance=include_performance
                )
                
                self.test_results[category] = result
        
        # Generate summary
        summary = self._generate_test_summary()
        
        return {
            "summary": summary,
            "test_results": self.test_results,
            "service_health": service_health,
            "issues_found": self.issues_found,
            "execution_time": (datetime.now() - self.start_time).total_seconds()
        }
    
    def _generate_test_summary(self) -> Dict[str, Any]:
        """Generate test execution summary."""
        total_categories = len(self.test_results)
        successful_categories = sum(1 for r in self.test_results.values() if r["success"])
        
        total_execution_time = sum(r["execution_time"] for r in self.test_results.values())
        
        # Calculate test statistics
        total_tests = 0
        total_passed = 0
        total_failed = 0
        
        for result in self.test_results.values():
            if "statistics" in result:
                stats = result["statistics"]
                total_tests += sum(stats.values())
                total_passed += stats.get("passed", 0)
                total_failed += stats.get("failed", 0)
        
        return {
            "total_categories": total_categories,
            "successful_categories": successful_categories,
            "category_success_rate": successful_categories / max(1, total_categories),
            "total_execution_time": total_execution_time,
            "total_tests": total_tests,
            "total_passed": total_passed,
            "total_failed": total_failed,
            "test_success_rate": total_passed / max(1, total_tests),
            "total_issues": len(self.issues_found)
        }
    
    def generate_report(self, output_file: str = "e2e_test_report.json"):
        """Generate comprehensive test report."""
        report = {
            "execution_metadata": {
                "start_time": self.start_time.isoformat(),
                "end_time": datetime.now().isoformat(),
                "total_duration": (datetime.now() - self.start_time).total_seconds()
            },
            "test_results": self.test_results,
            "issues_found": self.issues_found,
            "recommendations": self._generate_recommendations()
        }
        
        # Save report
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Test report generated: {output_file}")
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []
        
        # Analyze issues and generate recommendations
        issue_types = {}
        for issue in self.issues_found:
            issue_type = issue["type"]
            issue_types[issue_type] = issue_types.get(issue_type, 0) + 1
        
        if "connection_error" in issue_types:
            recommendations.append("Check MCP service connectivity and ensure all services are running")
        
        if "timeout_error" in issue_types:
            recommendations.append("Investigate performance issues - tests are taking too long to execute")
        
        if "assertion_error" in issue_types:
            recommendations.append("Review test assertions and expected behavior - may indicate implementation gaps")
        
        if "import_error" in issue_types:
            recommendations.append("Check Python dependencies and module imports")
        
        # Performance recommendations
        total_time = sum(r["execution_time"] for r in self.test_results.values())
        if total_time > 300:  # 5 minutes
            recommendations.append("Consider optimizing test execution time - current execution is slow")
        
        return recommendations


async def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Run comprehensive E2E tests")
    parser.add_argument("--real-services", action="store_true", help="Run tests with real MCP services")
    parser.add_argument("--performance", action="store_true", help="Include performance tests")
    parser.add_argument("--tdd-only", action="store_true", help="Run only TDD tests")
    parser.add_argument("--generate-report", action="store_true", help="Generate comprehensive test report")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create test runner
    runner = ComprehensiveTestRunner()
    
    try:
        # Run tests
        results = await runner.run_comprehensive_tests(
            include_real_services=args.real_services,
            include_performance=args.performance,
            tdd_only=args.tdd_only
        )
        
        # Print summary
        summary = results["summary"]
        print(f"\n{'='*60}")
        print("COMPREHENSIVE E2E TEST EXECUTION SUMMARY")
        print(f"{'='*60}")
        print(f"Categories: {summary['successful_categories']}/{summary['total_categories']} passed")
        print(f"Tests: {summary['total_passed']}/{summary['total_tests']} passed")
        print(f"Success Rate: {summary['test_success_rate']:.1%}")
        print(f"Execution Time: {summary['total_execution_time']:.2f}s")
        print(f"Issues Found: {summary['total_issues']}")
        
        # Generate report if requested
        if args.generate_report:
            report = runner.generate_report()
            print(f"\nDetailed report saved to: e2e_test_report.json")
        
        # Exit with appropriate code
        if summary["category_success_rate"] >= 0.8:  # 80% success rate
            print("\n✓ E2E tests completed successfully")
            sys.exit(0)
        else:
            print("\n✗ E2E tests completed with issues")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        print(f"\n✗ Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
