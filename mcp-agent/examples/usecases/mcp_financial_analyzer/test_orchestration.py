"""
Integration Tests for Orchestrated Financial Analysis System
===========================================================

Comprehensive test suite that validates the orchestration system
using the same scenarios from the original test files but executed
through the new orchestration layer.

Test Categories:
- Query processing and classification
- Workflow pattern selection
- Context management between agents
- Error handling and recovery
- Performance and statistics
"""

import asyncio
import logging
import pytest
from typing import Dict, Any, List
from datetime import datetime

# Import orchestration components
from orchestrator.query_processor import FinancialQueryProcessor, QueryType, WorkflowPattern
from orchestrator.context_manager import ContextManager
from orchestrator.workflow_patterns import WorkflowPatternRegistry
from orchestrator.orchestration_runner import create_orchestration_runner

# Import agents for testing
from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from agents.alert_manager_agent import create_alert_manager_agent  
from agents.mysql_agent import create_mysql_orchestrator_agent

# Set up test logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OrchestrationTestSuite:
    """Comprehensive test suite for orchestration system."""
    
    def __init__(self):
        """Initialize test suite."""
        self.query_processor = None
        self.context_manager = None
        self.pattern_registry = None
        self.orchestration_runner = None
        
        # Test scenarios from the original test files
        self.test_scenarios = [
            {
                "id": "shortage_gpu_crisis",
                "name": "Mass Production GPU Shortage Crisis",
                "query": "Analyze customer order CUSTORD-202506001 for G7B Golden_1 servers requiring MM2004 80GB GPUs. Current stock is 150, required is 4000.",
                "expected_type": QueryType.SHORTAGE_ANALYSIS,
                "expected_pattern": WorkflowPattern.FULL_WORKFLOW,
                "entities_expected": ["CUSTORD-202506001", "MM2004", "G7B"],
                "parameters_expected": {"quantities": [150, 4000]}
            },
            {
                "id": "supplier_reliability",
                "name": "Supplier Reliability Crisis",
                "query": "Analyze supplier MetaMind Technology and their DEP9005 CPUs for G8D server production delays",
                "expected_type": QueryType.SUPPLIER_RISK,
                "expected_pattern": WorkflowPattern.MYSQL_SHORTAGE,
                "entities_expected": ["MetaMind Technology", "DEP9005", "G8D"],
                "parameters_expected": {}
            },
            {
                "id": "customer_priority_conflict",
                "name": "Multi-Customer Resource Conflict",
                "query": "Analyze customer priority conflict between Tech Pioneer Co and QCT Technology for 800W power supplies",
                "expected_type": QueryType.CUSTOMER_PRIORITY,
                "expected_pattern": WorkflowPattern.MYSQL_ALERT,
                "entities_expected": ["Tech Pioneer", "QCT Technology", "800W"],
                "parameters_expected": {}
            },
            {
                "id": "comprehensive_stress_test",
                "name": "End-to-End Supply Chain Stress Test",
                "query": "Perform comprehensive supply chain analysis for all customer orders, work orders WO-202506001 through WO-202506003, and supplier capabilities",
                "expected_type": QueryType.COMPREHENSIVE,
                "expected_pattern": WorkflowPattern.FULL_WORKFLOW,
                "entities_expected": ["WO-202506001", "WO-202506002", "WO-202506003"],
                "parameters_expected": {}
            }
        ]
    
    async def setup_test_environment(self) -> bool:
        """Set up test environment with mock agents."""
        try:
            # Initialize components
            self.query_processor = FinancialQueryProcessor()
            self.context_manager = ContextManager(persist_context=False)  # No persistence for tests
            self.pattern_registry = WorkflowPatternRegistry()
            
            logger.info("✓ Test environment components initialized")
            return True
            
        except Exception as e:
            logger.error(f"Test environment setup failed: {e}")
            return False
    
    def test_query_processing(self) -> Dict[str, bool]:
        """Test query processing and classification."""
        logger.info("\n=== Testing Query Processing ===")
        results = {}
        
        for scenario in self.test_scenarios:
            scenario_id = scenario["id"]
            logger.info(f"Testing scenario: {scenario['name']}")
            
            try:
                # Parse query
                parsed = self.query_processor.process_query(scenario["query"])
                
                # Validate query type
                type_correct = parsed.query_type == scenario["expected_type"]
                results[f"{scenario_id}_type"] = type_correct
                logger.info(f"  Query type: {'✓' if type_correct else '✗'} "
                           f"(expected {scenario['expected_type'].value}, got {parsed.query_type.value})")
                
                # Validate workflow pattern  
                pattern_correct = parsed.workflow_pattern == scenario["expected_pattern"]
                results[f"{scenario_id}_pattern"] = pattern_correct
                logger.info(f"  Workflow pattern: {'✓' if pattern_correct else '✗'} "
                           f"(expected {scenario['expected_pattern'].value}, got {parsed.workflow_pattern.value})")
                
                # Validate entity extraction
                entities_found = []
                for entity_type, entity_list in parsed.entities.get_all_entities().items():
                    entities_found.extend(entity_list)
                
                entities_correct = all(
                    any(expected in found for found in entities_found)
                    for expected in scenario["entities_expected"]
                )
                results[f"{scenario_id}_entities"] = entities_correct
                logger.info(f"  Entity extraction: {'✓' if entities_correct else '✗'}")
                
                # Validate confidence
                confidence_ok = parsed.confidence >= 0.5
                results[f"{scenario_id}_confidence"] = confidence_ok
                logger.info(f"  Confidence: {'✓' if confidence_ok else '✗'} ({parsed.confidence:.2f})")
                
                # Validate clarity
                is_clear = self.query_processor.is_query_clear(parsed)
                results[f"{scenario_id}_clarity"] = is_clear
                logger.info(f"  Query clarity: {'✓' if is_clear else '✗'}")
                
            except Exception as e:
                logger.error(f"  Query processing failed: {e}")
                results[f"{scenario_id}_error"] = False
        
        return results
    
    def test_context_management(self) -> Dict[str, bool]:
        """Test context management system."""
        logger.info("\n=== Testing Context Management ===")
        results = {}
        
        try:
            # Create test workflow context
            workflow_id = "test_workflow_001"
            context = self.context_manager.create_context(
                workflow_id=workflow_id,
                query="Test query for context management",
                query_type="shortage_analysis",
                workflow_pattern="full_workflow"
            )
            
            # Test context creation
            results["context_creation"] = context is not None
            logger.info(f"  Context creation: {'✓' if results['context_creation'] else '✗'}")
            
            # Test MySQL context update
            mysql_data = {
                "response": "Test MySQL response",
                "reasoning": "Test reasoning",
                "success": True
            }
            self.context_manager.update_mysql_context(workflow_id, mysql_data, 1.5)
            
            context = self.context_manager.active_contexts[workflow_id]
            mysql_updated = context.mysql_context is not None
            results["mysql_context_update"] = mysql_updated
            logger.info(f"  MySQL context update: {'✓' if mysql_updated else '✗'}")
            
            # Test shortage context update
            shortage_data = {
                "company_name": "TestCompany",
                "shortage_index": 0.75,
                "risk_level": "HIGH",
                "response": "Test shortage response",
                "success": True
            }
            self.context_manager.update_shortage_context(workflow_id, shortage_data, 2.0)
            
            shortage_updated = context.shortage_context is not None
            results["shortage_context_update"] = shortage_updated
            logger.info(f"  Shortage context update: {'✓' if shortage_updated else '✗'}")
            
            # Test alert context update
            alert_data = {
                "company_name": "TestCompany",
                "alerts_sent": ["alert_1", "alert_2"],
                "notification_results": ["delivered", "delivered"],
                "alert_summary": "Test alert summary",
                "success": True
            }
            self.context_manager.update_alert_context(workflow_id, alert_data, 1.0)
            
            alert_updated = context.alert_context is not None
            results["alert_context_update"] = alert_updated
            logger.info(f"  Alert context update: {'✓' if alert_updated else '✗'}")
            
            # Test context retrieval for agents
            mysql_context = self.context_manager.get_context_for_agent(workflow_id, "mysql_analyzer")
            context_retrieval = len(mysql_context) > 0
            results["context_retrieval"] = context_retrieval
            logger.info(f"  Context retrieval: {'✓' if context_retrieval else '✗'}")
            
            # Test context summary
            summary = self.context_manager.get_full_context_summary(workflow_id)
            summary_complete = "workflow_id" in summary and "status" in summary
            results["context_summary"] = summary_complete
            logger.info(f"  Context summary: {'✓' if summary_complete else '✗'}")
            
            # Cleanup
            self.context_manager.cleanup_context(workflow_id)
            cleanup_success = workflow_id not in self.context_manager.active_contexts
            results["context_cleanup"] = cleanup_success
            logger.info(f"  Context cleanup: {'✓' if cleanup_success else '✗'}")
            
        except Exception as e:
            logger.error(f"Context management test failed: {e}")
            results["context_management_error"] = False
        
        return results
    
    def test_workflow_patterns(self) -> Dict[str, bool]:
        """Test workflow pattern registry and execution planning."""
        logger.info("\n=== Testing Workflow Patterns ===")
        results = {}
        
        try:
            # Test pattern registry
            patterns = self.pattern_registry.get_all_patterns()
            patterns_loaded = len(patterns) > 0
            results["patterns_loaded"] = patterns_loaded
            logger.info(f"  Patterns loaded: {'✓' if patterns_loaded else '✗'} ({len(patterns)} patterns)")
            
            # Test specific patterns
            shortage_pattern = self.pattern_registry.get_pattern("shortage_analysis")
            shortage_exists = shortage_pattern is not None
            results["shortage_pattern_exists"] = shortage_exists
            logger.info(f"  Shortage pattern exists: {'✓' if shortage_exists else '✗'}")
            
            if shortage_pattern:
                # Test execution order
                execution_order = shortage_pattern.get_execution_order()
                order_correct = len(execution_order) == 3  # MySQL -> Shortage -> Alert
                results["execution_order"] = order_correct
                logger.info(f"  Execution order: {'✓' if order_correct else '✗'} ({len(execution_order)} steps)")
            
            # Test pattern selection for query types
            for query_type in ["shortage_analysis", "supplier_risk", "customer_priority", "comprehensive"]:
                pattern = self.pattern_registry.get_pattern_for_query_type(query_type)
                pattern_found = pattern is not None
                results[f"pattern_for_{query_type}"] = pattern_found
                logger.info(f"  Pattern for {query_type}: {'✓' if pattern_found else '✗'}")
            
        except Exception as e:
            logger.error(f"Workflow patterns test failed: {e}")
            results["workflow_patterns_error"] = False
        
        return results
    
    async def test_orchestration_integration(self) -> Dict[str, bool]:
        """Test integration with mock orchestration system."""
        logger.info("\n=== Testing Orchestration Integration ===")
        results = {}
        
        try:
            # Test component integration
            components_initialized = all([
                self.query_processor is not None,
                self.context_manager is not None,
                self.pattern_registry is not None
            ])
            results["components_initialized"] = components_initialized
            logger.info(f"  Components initialized: {'✓' if components_initialized else '✗'}")
            
            # Test query processing integration
            test_query = "Analyze shortage for order CUSTORD-202506001"
            parsed_query = self.query_processor.process_query(test_query)
            
            # Create context for the query
            workflow_id = "integration_test_001"
            context = self.context_manager.create_context(
                workflow_id=workflow_id,
                query=test_query,
                query_type=parsed_query.query_type.value,
                workflow_pattern=parsed_query.workflow_pattern.value
            )
            
            integration_success = context is not None and parsed_query.confidence > 0.3
            results["integration_test"] = integration_success
            logger.info(f"  Integration test: {'✓' if integration_success else '✗'}")
            
            # Test execution plan generation
            execution_plan = self.query_processor.get_execution_plan(parsed_query)
            plan_valid = "workflow_pattern" in execution_plan and "required_agents" in execution_plan
            results["execution_plan"] = plan_valid
            logger.info(f"  Execution plan: {'✓' if plan_valid else '✗'}")
            
            # Cleanup
            self.context_manager.cleanup_context(workflow_id)
            
        except Exception as e:
            logger.error(f"Orchestration integration test failed: {e}")
            results["integration_error"] = False
        
        return results
    
    def run_performance_tests(self) -> Dict[str, Any]:
        """Run performance tests on the orchestration components."""
        logger.info("\n=== Testing Performance ===")
        results = {}
        
        try:
            # Test query processing performance
            start_time = datetime.now()
            
            for _ in range(100):  # Process 100 queries
                query = "Analyze shortage for order CUSTORD-202506001 with MM2004 GPUs stock 150 required 4000"
                parsed = self.query_processor.process_query(query)
            
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            avg_processing_time = processing_time / 100
            
            results["query_processing_performance"] = {
                "total_time": processing_time,
                "average_per_query": avg_processing_time,
                "queries_per_second": 100 / processing_time
            }
            
            performance_acceptable = avg_processing_time < 0.1  # Less than 100ms per query
            results["performance_acceptable"] = performance_acceptable
            logger.info(f"  Query processing performance: {'✓' if performance_acceptable else '✗'} "
                       f"({avg_processing_time:.3f}s avg, {100/processing_time:.1f} qps)")
            
            # Test context management performance
            start_time = datetime.now()
            
            for i in range(50):  # Create and cleanup 50 contexts
                workflow_id = f"perf_test_{i:03d}"
                context = self.context_manager.create_context(
                    workflow_id=workflow_id,
                    query="Performance test query",
                    query_type="shortage_analysis",
                    workflow_pattern="full_workflow"
                )
                self.context_manager.cleanup_context(workflow_id)
            
            end_time = datetime.now()
            context_time = (end_time - start_time).total_seconds()
            avg_context_time = context_time / 50
            
            results["context_management_performance"] = {
                "total_time": context_time,
                "average_per_context": avg_context_time,
                "contexts_per_second": 50 / context_time
            }
            
            context_performance_acceptable = avg_context_time < 0.05  # Less than 50ms per context
            results["context_performance_acceptable"] = context_performance_acceptable
            logger.info(f"  Context management performance: {'✓' if context_performance_acceptable else '✗'} "
                       f"({avg_context_time:.3f}s avg, {50/context_time:.1f} cps)")
            
        except Exception as e:
            logger.error(f"Performance tests failed: {e}")
            results["performance_error"] = str(e)
        
        return results
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all orchestration tests."""
        logger.info("=== Starting Orchestration Test Suite ===")
        
        # Setup test environment
        if not await self.setup_test_environment():
            return {"error": "Failed to setup test environment"}
        
        # Run all test categories
        all_results = {}
        
        # Query processing tests
        all_results["query_processing"] = self.test_query_processing()
        
        # Context management tests
        all_results["context_management"] = self.test_context_management()
        
        # Workflow patterns tests
        all_results["workflow_patterns"] = self.test_workflow_patterns()
        
        # Integration tests
        all_results["orchestration_integration"] = await self.test_orchestration_integration()
        
        # Performance tests
        all_results["performance"] = self.run_performance_tests()
        
        # Calculate overall statistics
        total_tests = 0
        passed_tests = 0
        
        for category, results in all_results.items():
            if isinstance(results, dict):
                for test_name, test_result in results.items():
                    if isinstance(test_result, bool):
                        total_tests += 1
                        if test_result:
                            passed_tests += 1
        
        all_results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "pass_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0
        }
        
        logger.info(f"\n=== Test Suite Summary ===")
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Pass Rate: {all_results['summary']['pass_rate']:.1f}%")
        
        return all_results


async def main():
    """Main test runner."""
    test_suite = OrchestrationTestSuite()
    results = await test_suite.run_all_tests()
    
    # Return appropriate exit code
    summary = results.get("summary", {})
    pass_rate = summary.get("pass_rate", 0)
    
    if pass_rate >= 80:  # 80% pass rate required
        logger.info("✓ Test suite PASSED")
        return True
    else:
        logger.error("✗ Test suite FAILED")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)