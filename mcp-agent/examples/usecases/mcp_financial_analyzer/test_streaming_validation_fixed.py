"""Comprehensive streaming response validation and performance testing.

Tests real-time streaming functionality, latency compliance with <100ms requirements,
context passing during streaming, error handling in streaming operations, and
concurrent streaming workflows.
"""

import asyncio
import time
import pytest
import pytest_asyncio
from typing import Dict, Any, List, Optional, AsyncGenerator

# Add the parent directory to sys.path for imports
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from unittest.mock import AsyncMock, patch, MagicMock
from contextlib import asynccontextmanager

from orchestrator.orchestration_runner import OrchestrationRunner
from orchestrator.financial_orchestrator import FinancialOrchestrator
from orchestrator.context_manager import ContextManager, FinancialWorkflowContext as WorkflowContext
# Fixed imports - use proper factory functions instead of aliases
from agents.mysql_agent import create_mysql_orchestrator_agent
from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from agents.alert_manager_agent import AlertManagerAgent


class StreamingTestHelper:
    """Helper class for streaming test utilities."""
    
    @staticmethod
    async def create_mock_streaming_generator(
        chunks: List[Dict[str, Any]], 
        chunk_delay: float = 0.05,
        simulate_errors: bool = False
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Create mock streaming data generator."""
        for i, chunk in enumerate(chunks):
            if simulate_errors and i == len(chunks) // 2:
                raise Exception(f"Simulated streaming error at chunk {i}")
            
            await asyncio.sleep(chunk_delay)
            yield chunk
    
    @staticmethod
    async def measure_streaming_latency(
        stream_generator: AsyncGenerator, 
        max_chunks: int = 100
    ) -> Dict[str, Any]:
        """Measure latency between streaming chunks."""
        latencies = []
        chunk_count = 0
        start_time = time.time()
        last_chunk_time = start_time
        
        try:
            async for chunk in stream_generator:
                current_time = time.time()
                if chunk_count > 0:
                    latency = (current_time - last_chunk_time) * 1000  # Convert to ms
                    latencies.append(latency)
                
                last_chunk_time = current_time
                chunk_count += 1
                
                if chunk_count >= max_chunks:
                    break
        except Exception as e:
            pass  # Handle streaming errors gracefully for measurement
        
        total_time = time.time() - start_time
        
        return {
            'total_chunks': chunk_count,
            'total_time_ms': total_time * 1000,
            'latencies_ms': latencies,
            'avg_latency_ms': sum(latencies) / len(latencies) if latencies else 0,
            'max_latency_ms': max(latencies) if latencies else 0,
            'min_latency_ms': min(latencies) if latencies else 0
        }


class TestRealTimeStreamingFunctionality:
    """Test streaming responses and real-time data flow."""

    @pytest.fixture
    def streaming_test_data(self):
        """Test data for streaming scenarios."""
        return {
            'mysql_stream_chunks': [
                {'type': 'data', 'content': {'table': 'orders', 'rows_processed': 100}},
                {'type': 'data', 'content': {'table': 'materials', 'rows_processed': 250}},
                {'type': 'data', 'content': {'table': 'suppliers', 'rows_processed': 75}},
                {'type': 'complete', 'content': {'total_rows': 425, 'processing_time': 2.1}}
            ],
            'shortage_stream_chunks': [
                {'type': 'analysis', 'content': {'stage': 'data_collection', 'progress': 25}},
                {'type': 'analysis', 'content': {'stage': 'shortage_calculation', 'progress': 50}},
                {'type': 'analysis', 'content': {'stage': 'risk_assessment', 'progress': 75}},
                {'type': 'result', 'content': {'shortage_index': 0.75, 'high_risk_materials': 12}}
            ],
            'alert_stream_chunks': [
                {'type': 'preparation', 'content': {'alerts_prepared': 5}},
                {'type': 'sending', 'content': {'channel': 'email', 'sent': 3}},
                {'type': 'sending', 'content': {'channel': 'slack', 'sent': 2}},
                {'type': 'complete', 'content': {'total_sent': 5, 'delivery_time': 1.2}}
            ]
        }