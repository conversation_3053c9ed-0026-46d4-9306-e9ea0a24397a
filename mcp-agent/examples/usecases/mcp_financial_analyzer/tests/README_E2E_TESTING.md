# Comprehensive End-to-End Testing for Financial Analyzer Orchestration

## Overview

This directory contains comprehensive end-to-end tests for the financial analyzer orchestration workflow, implemented using Test-Driven Development (TDD) principles. The test suite validates the complete pipeline from user query input through MySQL agent, shortage analysis agent, and alert manager agent execution.

## Test Architecture

### Test Categories

1. **Complete Workflow Pipeline Tests** (`TestCompleteWorkflowPipeline`)
   - Validates end-to-end workflow execution
   - Tests user query → MySQL → Shortage → Alert pipeline
   - Verifies proper sequencing and data flow between agents

2. **Context Sharing Validation Tests** (`TestContextSharingValidation`)
   - Tests context creation and persistence
   - Validates data flow between agents
   - Tests error handling and recovery mechanisms

3. **Real Data Integration Tests** (`TestRealDataIntegration`)
   - Uses actual MCP server connections
   - Tests with realistic financial scenarios
   - Validates against real MySQL database schemas

4. **Orchestration Validation Tests** (`TestOrchestrationValidation`)
   - Tests query intent interpretation and routing
   - Validates agent coordination and sequencing
   - Tests error handling and graceful degradation

5. **Performance and Load Tests** (`TestPerformanceAndLoad`)
   - Validates <100ms streaming latency requirements
   - Tests concurrent workflow execution
   - Monitors memory usage and resource management

6. **TDD Workflow Pattern Tests** (`TestTDDWorkflowPatternDefinition`)
   - Defines expected workflow pattern behavior
   - Tests pattern execution logic
   - Validates pattern configuration and dependencies

## Test-Driven Development Approach

### TDD Cycle Implementation

1. **Red Phase**: Write failing tests that define expected behavior
2. **Green Phase**: Implement minimal code to make tests pass
3. **Refactor Phase**: Improve code while maintaining test coverage

### Example TDD Test Structure

```python
@pytest.mark.asyncio
async def test_complete_shortage_analysis_workflow(self, orchestration_runner, realistic_queries):
    """
    TDD Test: Complete shortage analysis workflow from query to alerts.
    
    This test defines the expected behavior for a complete workflow:
    1. User submits shortage analysis query
    2. Query is parsed and routed correctly
    3. MySQL agent analyzes historical data
    4. Shortage analyzer calculates shortage indices
    5. Alert manager generates and sends notifications
    6. Context is shared properly between all agents
    7. Final results are compiled and returned
    """
    # Test implementation that initially fails until orchestration is complete
    result = await runner.execute_financial_query(query=query, execution_mode="pattern_based")
    
    # Comprehensive assertions that define expected behavior
    assert result["success"] is True
    assert "workflow_id" in result
    assert result["query_analysis"]["confidence"] > 0.6
    # ... additional assertions
```

## Test Execution

### Prerequisites

1. **Python Dependencies**
   ```bash
   pip install pytest pytest-asyncio aiohttp psutil
   ```

2. **MCP Services** (for real data tests)
   - MySQL MCP server running on localhost:8702
   - Shortage analyzer MCP server on localhost:6970
   - Alert manager MCP server on localhost:6972

### Running Tests

#### Basic Test Execution
```bash
# Run all tests
python -m pytest tests/ -v

# Run specific test category
python -m pytest tests/test_comprehensive_e2e_orchestration.py::TestCompleteWorkflowPipeline -v

# Run TDD tests only
python -m pytest tests/ -m tdd -v
```

#### Comprehensive Test Execution
```bash
# Run comprehensive test suite
python run_comprehensive_e2e_tests.py

# Run with real MCP services
python run_comprehensive_e2e_tests.py --real-services

# Run with performance testing
python run_comprehensive_e2e_tests.py --performance

# Run TDD tests only
python run_comprehensive_e2e_tests.py --tdd-only

# Generate detailed report
python run_comprehensive_e2e_tests.py --generate-report
```

#### Test Markers
```bash
# Run end-to-end integration tests
python -m pytest -m e2e

# Run performance tests
python -m pytest -m performance

# Run tests requiring real MCP services
python -m pytest -m real_service

# Run context sharing tests
python -m pytest -m context_sharing
```

## Test Configuration

### Environment Variables

```bash
# Test environment configuration
export ORCHESTRATOR_ENVIRONMENT=test
export ORCHESTRATOR_LOG_LEVEL=INFO
export ORCHESTRATOR_CONTEXT_DIR=./test_contexts
export ORCHESTRATOR_MAX_WORKFLOWS=5

# MCP service endpoints
export MYSQL_SERVER_HOST=localhost
export MYSQL_SERVER_PORT=8702
export SHORTAGE_SERVER_HOST=localhost
export SHORTAGE_SERVER_PORT=6970
export ALERT_SERVER_HOST=localhost
export ALERT_SERVER_PORT=6972
```

### Performance Thresholds

- **Maximum workflow execution time**: 60 seconds
- **Maximum agent execution time**: 30 seconds per agent
- **Streaming latency requirement**: <100ms
- **Memory usage threshold**: 500MB
- **Expected success rate**: 95%

## Test Data and Scenarios

### Realistic Financial Scenarios

1. **GPU Shortage Crisis**
   ```
   Query: "Analyze shortage for customer order CUSTORD-202506001 requiring MM2004 80GB GPUs for G7B Golden_1 servers"
   Expected: HIGH shortage index, CRITICAL alerts
   ```

2. **Supplier Reliability Crisis**
   ```
   Query: "Evaluate supplier AVATA Technology delivery performance for DEP9005 CPUs"
   Expected: Supplier risk analysis, delivery performance metrics
   ```

3. **Multi-Customer Resource Conflict**
   ```
   Query: "Analyze resource allocation conflict between Tech Pioneer Co and QCT Technology for HCS500 motherboards"
   Expected: Resource conflict analysis, priority-based alerts
   ```

## Expected Test Results

### Success Criteria

- **All workflow pipeline tests pass**: Complete end-to-end execution
- **Context sharing validation**: Data flows correctly between agents
- **Real data integration**: Works with actual MCP services
- **Performance requirements met**: <100ms streaming latency
- **TDD tests define behavior**: Clear expected behavior definitions

### Common Issues and Resolutions

1. **MCP Service Connection Issues**
   - **Issue**: Tests fail with connection errors
   - **Resolution**: Ensure MCP services are running and accessible
   - **Command**: Check service health with `curl http://localhost:8702/sse`

2. **Context Sharing Failures**
   - **Issue**: Agents don't receive context from previous agents
   - **Resolution**: Verify context manager implementation
   - **Debug**: Check context persistence in `./test_contexts/`

3. **Performance Test Failures**
   - **Issue**: Tests exceed time or memory thresholds
   - **Resolution**: Optimize agent execution or adjust thresholds
   - **Monitor**: Use performance monitoring fixtures

4. **TDD Test Failures**
   - **Issue**: Tests fail because expected behavior isn't implemented
   - **Resolution**: This is expected in TDD - implement the missing functionality
   - **Process**: Follow Red → Green → Refactor cycle

## Test Reports and Analysis

### Automated Report Generation

The test suite generates comprehensive reports including:

- **Execution Summary**: Pass/fail rates, execution times
- **Performance Metrics**: Memory usage, latency measurements
- **Issue Analysis**: Categorized failures with recommendations
- **Service Health**: MCP service connectivity status

### Report Files

- `e2e_test_report.json`: Detailed test execution report
- `e2e_test_execution.log`: Execution logs with timestamps
- `benchmark_results.json`: Performance benchmark data (if enabled)

## Continuous Integration

### GitHub Actions Integration

```yaml
name: E2E Orchestration Tests
on: [push, pull_request]
jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run E2E tests
        run: python run_comprehensive_e2e_tests.py --generate-report
      - name: Upload test report
        uses: actions/upload-artifact@v2
        with:
          name: e2e-test-report
          path: e2e_test_report.json
```

## Contributing

### Adding New Tests

1. **Follow TDD Principles**
   - Write failing test first
   - Implement minimal functionality
   - Refactor while maintaining coverage

2. **Use Appropriate Test Categories**
   - Add to existing test classes or create new ones
   - Use proper pytest markers
   - Include performance considerations

3. **Document Expected Behavior**
   - Clear test docstrings
   - Realistic test scenarios
   - Comprehensive assertions

### Test Maintenance

- **Regular execution**: Run tests frequently during development
- **Performance monitoring**: Track execution times and resource usage
- **Issue tracking**: Document and resolve test failures promptly
- **Coverage analysis**: Ensure comprehensive test coverage

## Troubleshooting

### Debug Mode
```bash
# Run tests with verbose output
python run_comprehensive_e2e_tests.py --verbose

# Run specific failing test with debug info
python -m pytest tests/test_comprehensive_e2e_orchestration.py::TestCompleteWorkflowPipeline::test_complete_shortage_analysis_workflow -v -s --tb=long
```

### Log Analysis
```bash
# View execution logs
tail -f e2e_test_execution.log

# Check context files
ls -la test_contexts/

# Monitor system resources
htop
```

For additional support, refer to the orchestration implementation documentation and API reference.
