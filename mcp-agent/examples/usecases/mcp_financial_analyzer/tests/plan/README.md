# MCP Financial Analyzer - Comprehensive TDD Test Suite

This directory contains a comprehensive Test-Driven Development (TDD) test suite for the MCP financial analyzer orchestration workflow. The test suite follows TDD principles and provides extensive validation across all system components.

## Overview

The test suite validates the complete end-to-end financial analysis workflow: **User Query → MySQL Agent → Shortage Analysis Agent → Alert Manager Agent**. It tests real service integration, error handling, streaming functionality, performance characteristics, and data validation.

## Test Architecture

### Directory Structure
```
tests/plan/
├── conftest.py                     # Plan-specific fixtures and configuration
├── test-pipeline-validation.py     # End-to-end pipeline testing
├── test-orchestration-logic.py     # Query processing and agent coordination
├── test-real-service-integration.py # Live MCP server testing
├── test-error-propagation.py       # Error handling and recovery
├── test-streaming-validation.py    # Real-time streaming functionality
├── test-performance-validation.py  # Performance benchmarks and load testing
├── test-data-validation.py         # Financial data accuracy and validation
└── README.md                       # This documentation
```

### Test Organization
- **Naming Convention**: `test-[component].py` for clear component identification
- **Fixture Inheritance**: Extends main `conftest.py` without breaking global test infrastructure
- **Service Integration**: Uses `@pytest.mark.real_service` for tests requiring live MCP servers
- **TDD Approach**: Tests define expected behavior before implementation validation

## Test Categories

### 1. Pipeline Validation (`test-pipeline-validation.py`)

**Purpose**: Validates the complete workflow from user query to final alert delivery.

**Test Classes**:
- `TestCompleteWorkflowPipeline`: Full User Query → MySQL → Shortage → Alert workflow
- `TestAgentInputOutputValidation`: Schema compliance and data format validation  
- `TestWorkflowCompletionStatus`: Success/failure status reporting
- `TestDataTransformationAccuracy`: Data integrity during agent handoffs

**Key Validations**:
- Workflow execution returns actual financial data (not placeholders)
- Agent input/output schemas match `MCPOrchestratorInputSchema` and `AlertManagementInputSchema`
- Context data flows correctly between agents using `ContextManager.get_context_for_agent()`
- Error propagation and graceful degradation work properly

**Example Test**:
```python
async def test_full_workflow_with_realistic_financial_query(
    self, 
    orchestration_runner: OrchestrationRunner,
    realistic_financial_queries: List[Dict[str, Any]]
):
    result = await orchestration_runner.execute_financial_query(
        query="Check shortage status for order CUSTORD-***********",
        workflow_type='shortage_analysis'
    )
    
    assert result['status'] in ['completed', 'success']
    assert 'mysql_results' in result['results']
    assert 'shortage_analysis' in result['results']
```

### 2. Orchestration Logic (`test-orchestration-logic.py`)

**Purpose**: Tests query interpretation, agent routing, and workflow coordination.

**Test Classes**:
- `TestQueryInterpretationAndRouting`: Query classification and workflow pattern selection
- `TestAgentCoordinationAndSequencing`: Execution order and dependency management
- `TestContextSharingMechanisms`: Context persistence and recovery
- `TestConcurrentExecutionHandling`: Parallel workflow capabilities
- `TestWorkflowPatternExecution`: Different workflow patterns (shortage_analysis, supplier_risk, etc.)

**Key Validations**:
- `FinancialQueryProcessor.process_query()` correctly classifies queries
- Entity extraction for orders (CUSTORD-YYYYMMXXX), materials (MM2004), suppliers
- `WorkflowPatternRegistry.get_pattern_for_query_type()` selects appropriate patterns
- Context data sharing maintains consistency across agents
- Concurrent workflows respect `max_concurrent_workflows` limits

**Example Test**:
```python
async def test_query_classification_accuracy(
    self, 
    query_processor: FinancialQueryProcessor,
    realistic_financial_queries: List[Dict[str, Any]]
):
    for query_data in realistic_financial_queries:
        result = await query_processor.process_query(query_data['query'])
        
        assert 'query_type' in result
        assert 'confidence' in result
        assert 0.0 <= result['confidence'] <= 1.0
```

### 3. Real Service Integration (`test-real-service-integration.py`)

**Purpose**: Tests with actual MCP servers and live financial data.

**Test Classes**:
- `TestRealMCPServerConnections`: Connections to MySQL (8702), Shortage (6970), Alert (6971) services
- `TestLiveFinancialDataExecution`: Workflows with real database data
- `TestRealisticUserQueries`: Complex multi-entity queries
- `TestDatabaseSchemaValidation`: Actual database schema compliance
- `TestJSONLLogMonitoring`: JSONL log validation for TOOL_CALL events

**Key Validations**:
- Service health checks for all MCP servers
- Real database query execution and result validation
- Streaming response functionality with <100ms latency requirements
- JSONL logs contain proper TOOL_CALL_START/ARGS/END sequences
- Generated reports contain actual financial data (not placeholders)

**Example Test**:
```python
@pytest.mark.real_service
async def test_mysql_server_connection_and_query_execution(
    self,
    service_health_check,
    real_mysql_agent
):
    service_status = await service_health_check()
    if not service_status.get('mysql', False):
        pytest.skip("MySQL MCP service not available on port 8702")
    
    result = await real_mysql_agent.process_query(
        "SELECT COUNT(*) FROM orders WHERE status = 'pending'"
    )
    
    assert result['status'] == 'success'
    assert 'data' in result
```

### 4. Error Propagation (`test-error-propagation.py`)

**Purpose**: Comprehensive error handling and recovery testing.

**Test Classes**:
- `TestAgentLevelErrorHandling`: Individual agent error scenarios
- `TestWorkflowLevelErrorPropagation`: Error flow across workflow pipeline
- `TestGracefulDegradation`: System behavior during service failures
- `TestNetworkAndAPIFailureHandling`: Network timeouts and API failures
- `TestContextRecoveryMechanisms`: Context recovery from persisted state

**Key Validations**:
- `OrchestrationError`, `AgentExecutionError`, `ContextError` exception handling
- Partial workflow completion when some agents fail
- Circuit breaker functionality in alert notification system
- Context corruption recovery and backup/restore mechanisms
- Network timeout retry with exponential backoff

**Example Test**:
```python
async def test_mysql_agent_failure_propagation(
    self,
    orchestration_runner_with_failing_agents
):
    with pytest.raises(OrchestrationError) as exc_info:
        await runner.execute_financial_query(
            query="Test failure propagation",
            workflow_type="shortage_analysis"
        )
    
    error = exc_info.value
    assert error.failed_step == "mysql_query"
    assert error.original_error.agent_name == "mysql"
```

### 5. Streaming Validation (`test-streaming-validation.py`)

**Purpose**: Real-time streaming functionality and latency testing.

**Test Classes**:
- `TestStreamingResponseValidation`: Streaming across all workflow stages
- `TestStreamingLatencyPerformance`: <100ms latency compliance
- `TestContextPassingInStreaming`: Context consistency during streaming
- `TestStreamingErrorHandling`: Error handling in streaming operations
- `TestConcurrentStreamingWorkflows`: Multiple concurrent streams

**Key Validations**:
- Streaming latency under 100ms as required
- Context data consistency during streaming operations
- Memory usage doesn't grow excessively during streaming
- WebSocket streaming compliance per `WEBSOCKET_STREAMING_MIGRATION_GUIDE.md`
- Concurrent streaming workflow isolation

**Example Test**:
```python
async def test_end_to_end_streaming_latency(
    self,
    streaming_latency_validator
):
    e2e_stream = create_end_to_end_stream()
    latency_stats = await streaming_latency_validator(e2e_stream, max_latency_ms=100)
    
    assert latency_stats['avg_latency'] < 100
    assert latency_stats['max_latency'] < 150
```

### 6. Performance Validation (`test-performance-validation.py`)

**Purpose**: Performance benchmarks and load testing.

**Test Classes**:
- `TestWorkflowExecutionPerformance`: Execution time benchmarks
- `TestConcurrentWorkflowHandling`: Load testing with multiple workflows
- `TestMemoryAndResourceManagement`: Memory usage and resource cleanup
- `TestScalabilityValidation`: Performance scaling with load
- `TestPerformanceRegression`: Baseline comparison and regression detection

**Key Validations**:
- Workflow execution times meet performance targets
- Memory usage stays within defined limits
- Concurrent workflow handling respects resource limits
- Performance baselines and regression detection
- Resource cleanup after workflow completion

**Performance Benchmarks**:
```python
benchmarks = {
    'shortage_analysis': {'max_time': 5.0, 'max_memory': 50},
    'supplier_risk': {'max_time': 7.0, 'max_memory': 60}, 
    'comprehensive': {'max_time': 15.0, 'max_memory': 100}
}
```

### 7. Data Validation (`test-data-validation.py`)

**Purpose**: Financial data accuracy and entity validation.

**Test Classes**:
- `TestFinancialDataAccuracy`: Shortage indices, risk levels, financial ratios
- `TestEntityExtractionValidation`: Order numbers, material codes, supplier names
- `TestSchemaComplianceValidation`: Pydantic model validation
- `TestDataIntegrityAcrossAgents`: Data consistency between agent handoffs
- `TestRealisticFinancialScenarios`: Edge cases and realistic scenarios

**Key Validations**:
- Order numbers match patterns (CUSTORD-YYYYMMXXX, WO-YYYYMMXXX, PR-YYYYMMXXX)
- Material codes follow formats (MM2004, HCS500, DDR5_32GB, etc.)
- Shortage indices stay within 0.0-1.0 range
- Risk levels are valid (LOW/MEDIUM/HIGH/CRITICAL)
- Financial calculations maintain accuracy across transformations

**Example Test**:
```python
async def test_shortage_index_calculation_accuracy(self, sample_data):
    for material in sample_data['materials']:
        current_stock = material['current_stock']
        required_stock = material['required_stock']
        
        expected_index = max(0.0, min(1.0, (required_stock - current_stock) / required_stock))
        assert 0.0 <= expected_index <= 1.0
```

## Running the Tests

### Prerequisites

**Required Services**:
- MySQL MCP server (port 8702)
- Shortage analysis service (port 6970) 
- Alert notification service (port 6971)

**Environment Variables**:
```bash
export MCP_MYSQL_HOST=localhost
export MCP_MYSQL_PORT=8702
export MCP_SHORTAGE_HOST=localhost
export MCP_SHORTAGE_PORT=6970
export MCP_ALERT_HOST=localhost
export MCP_ALERT_PORT=6971
```

### Test Execution Commands

**Run all TDD tests**:
```bash
pytest tests/plan/ -v
```

**Run only real service tests**:
```bash
pytest tests/plan/ -m real_service -v
```

**Run tests excluding real services**:
```bash
pytest tests/plan/ -m "not real_service" -v
```

**Run specific test categories**:
```bash
# Pipeline validation
pytest tests/plan/test-pipeline-validation.py -v

# Orchestration logic  
pytest tests/plan/test-orchestration-logic.py -v

# Real service integration
pytest tests/plan/test-real-service-integration.py -m real_service -v

# Error handling
pytest tests/plan/test-error-propagation.py -v

# Streaming functionality
pytest tests/plan/test-streaming-validation.py -v

# Performance testing
pytest tests/plan/test-performance-validation.py -v

# Data validation
pytest tests/plan/test-data-validation.py -v
```

**Run with coverage**:
```bash
pytest tests/plan/ --cov=src --cov-report=html -v
```

**Run performance tests only**:
```bash
pytest tests/plan/ -k "performance" -v
```

## Test Data and Fixtures

### Fixture Organization

**Plan-specific fixtures** (`conftest.py`):
- `real_agent_factory`: Creates real agent instances
- `mock_agent_factory`: Creates sophisticated mock agents
- `realistic_financial_queries`: Sample queries for testing
- `realistic_financial_scenarios`: Edge case scenarios
- `performance_monitor`: Execution time tracking
- `memory_usage_monitor`: Memory usage tracking
- `streaming_latency_validator`: Streaming performance validation

**Inherited fixtures** (from main `conftest.py`):
- Database connections
- Service configurations  
- Authentication tokens
- Test data generators

### Mock vs Real Service Testing

**Mock Testing**: Used for unit tests, isolated component testing, and scenarios where real services aren't available.

**Real Service Testing**: Used for integration testing, end-to-end validation, and performance benchmarking with actual MCP servers.

## Performance Benchmarks

### Execution Time Targets

| Workflow Type | Target Duration | Memory Limit | Throughput |
|---------------|----------------|--------------|------------|
| shortage_analysis | 5.0s | 50MB | 0.2/s |
| supplier_risk | 7.0s | 60MB | 0.15/s |
| customer_priority | 4.0s | 40MB | 0.25/s |
| comprehensive | 15.0s | 100MB | 0.07/s |

### Streaming Requirements

- **Latency**: < 100ms between streaming chunks
- **Throughput**: > 200 chunks/second
- **Memory**: < 50MB increase during streaming
- **Concurrency**: Support 4+ concurrent streaming workflows

### Concurrent Workflow Limits

| Load Level | Concurrent Workflows | Expected Time | Memory Limit |
|-----------|---------------------|---------------|--------------|
| Light | 3 | 10.0s | 150MB |
| Medium | 5 | 15.0s | 250MB |
| Heavy | 8 | 25.0s | 400MB |

## Troubleshooting

### Common Test Failures

**Service Connection Issues**:
```bash
# Check service health
curl http://localhost:8702/health
curl http://localhost:6970/health  
curl http://localhost:6971/health

# Verify service logs
docker logs mcp-mysql-service
docker logs mcp-shortage-service
docker logs mcp-alert-service
```

**Schema Validation Errors**:
- Ensure Pydantic models are up to date
- Check for breaking changes in schema definitions
- Validate test data against current schemas

**Performance Test Failures**:
- Check system resources (CPU, memory)
- Ensure no other processes are consuming resources
- Validate performance baselines are realistic for test environment

**Streaming Test Issues**:
- Verify WebSocket support is enabled
- Check network latency in test environment
- Ensure streaming endpoints are properly configured

### Log Analysis and Debugging

**JSONL Log Analysis**:
```python
# Parse JSONL logs for debugging
from pathlib import Path
import json

log_entries = []
with open('orchestration.jsonl', 'r') as f:
    for line in f:
        log_entries.append(json.loads(line))

# Filter for tool call events
tool_calls = [entry for entry in log_entries if entry.get('event_type') == 'TOOL_CALL_START']
```

**Performance Analysis**:
```bash
# Generate performance report
pytest tests/plan/test-performance-validation.py --benchmark-only --benchmark-sort=mean

# Memory profiling
pytest tests/plan/ --profile --profile-sort=cumulative
```

### Service Health Verification

**Manual Service Testing**:
```python
import httpx
import asyncio

async def check_services():
    services = {
        'mysql': 'http://localhost:8702/health',
        'shortage': 'http://localhost:6970/health', 
        'alert': 'http://localhost:6971/health'
    }
    
    async with httpx.AsyncClient() as client:
        for name, url in services.items():
            try:
                response = await client.get(url, timeout=5.0)
                print(f"{name}: {'HEALTHY' if response.status_code == 200 else 'UNHEALTHY'}")
            except Exception as e:
                print(f"{name}: ERROR - {e}")

asyncio.run(check_services())
```

## Integration with CI/CD

### Test Markers and Categorization

**Test Markers**:
- `@pytest.mark.real_service`: Requires live MCP services
- `@pytest.mark.performance`: Performance and load tests
- `@pytest.mark.slow`: Long-running tests (>30s)
- `@pytest.mark.integration`: Integration tests
- `@pytest.mark.unit`: Unit tests

**CI/CD Pipeline Integration**:
```yaml
# Example GitHub Actions workflow
name: MCP Financial Analyzer Tests

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run unit tests
        run: pytest tests/plan/ -m "not real_service" -v

  integration-tests:
    runs-on: ubuntu-latest
    services:
      mysql: # MySQL service configuration
      shortage: # Shortage service configuration  
      alert: # Alert service configuration
    steps:
      - uses: actions/checkout@v3
      - name: Run integration tests
        run: pytest tests/plan/ -m real_service -v

  performance-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run performance tests
        run: pytest tests/plan/ -m performance --benchmark-json=benchmark.json
      - name: Store benchmark results
        uses: benchmark-action/github-action-benchmark@v1
        with:
          benchmark-data-dir-path: benchmark.json
```

### Automated Test Execution Strategies

**Pull Request Validation**:
- Run unit tests and mock-based integration tests
- Performance regression detection
- Schema validation tests

**Nightly Builds**:
- Full test suite including real service tests
- Load testing and stress testing
- Performance baseline updates

**Release Validation**:
- Complete end-to-end testing with production-like data
- Performance benchmarking
- Security and compliance testing

## Contributing

### Adding New Tests

**Test File Structure**:
```python
"""Module docstring describing test purpose."""

import pytest
import pytest_asyncio
from typing import Dict, Any, List

from ...src.orchestration.orchestration_runner import OrchestrationRunner
# Import other necessary components

class TestYourComponent:
    """Test class with clear description."""
    
    @pytest.fixture
    def your_fixture(self):
        """Fixture description."""
        return test_data
    
    async def test_your_functionality(self, your_fixture):
        """Test method with descriptive name."""
        # Arrange
        input_data = your_fixture
        
        # Act  
        result = await component.method(input_data)
        
        # Assert
        assert result is not None
        assert result['status'] == 'success'
```

**Test Naming Conventions**:
- Test files: `test-[component-name].py`
- Test classes: `TestComponentName`
- Test methods: `test_specific_behavior_being_tested`

**Documentation Requirements**:
- Clear docstrings for test classes and methods
- Comments explaining complex test logic
- Update README.md when adding new test categories

### Best Practices

1. **Follow TDD Principles**: Write tests that define expected behavior
2. **Use Realistic Data**: Test with actual financial scenarios
3. **Validate Error Conditions**: Test both success and failure paths
4. **Performance Awareness**: Include performance assertions in tests
5. **Mock Appropriately**: Use mocks for isolation, real services for integration
6. **Clean Up Resources**: Ensure proper cleanup in fixtures and tests
7. **Document Assumptions**: Clearly document test assumptions and requirements

This comprehensive test suite ensures the MCP financial analyzer system is thoroughly validated across all dimensions: functionality, performance, reliability, and data accuracy.