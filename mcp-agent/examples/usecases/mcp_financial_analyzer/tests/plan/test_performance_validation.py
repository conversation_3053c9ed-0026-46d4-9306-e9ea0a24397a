"""Comprehensive performance and load testing for the orchestration system.

Tests workflow execution performance, concurrent workflow handling, memory and resource management,
scalability validation, and performance regression detection.
"""

import asyncio
import time
import gc
import pytest
import pytest_asyncio
from typing import Dict, Any, List, Optional, Tuple

# Add the parent directory to sys.path for imports
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from unittest.mock import AsyncMock, patch
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

from orchestrator.orchestration_runner import OrchestrationRunner
from orchestrator.financial_orchestrator import FinancialOrchestrator
from orchestrator.context_manager import ContextManager
from orchestrator.workflow_patterns import WorkflowPattern, ExecutionMode


@dataclass
class PerformanceBenchmark:
    """Performance benchmark data structure."""
    operation_name: str
    target_duration: float
    target_throughput: float
    max_memory_mb: float
    max_cpu_percent: float


class TestWorkflowExecutionPerformance:
    """Validate execution times and performance benchmarks."""

    @pytest.fixture
    def performance_benchmarks(self):
        """Define performance benchmarks for different workflow types."""
        return {
            'shortage_analysis': PerformanceBenchmark(
                operation_name='shortage_analysis',
                target_duration=5.0,      # 5 seconds max
                target_throughput=0.2,    # 0.2 workflows/second
                max_memory_mb=50,         # 50MB max increase
                max_cpu_percent=80        # 80% CPU max
            ),
            'supplier_risk': PerformanceBenchmark(
                operation_name='supplier_risk',
                target_duration=7.0,      # 7 seconds max
                target_throughput=0.15,   # 0.15 workflows/second
                max_memory_mb=60,         # 60MB max increase
                max_cpu_percent=85        # 85% CPU max
            ),
            'customer_priority': PerformanceBenchmark(
                operation_name='customer_priority',
                target_duration=4.0,      # 4 seconds max
                target_throughput=0.25,   # 0.25 workflows/second
                max_memory_mb=40,         # 40MB max increase
                max_cpu_percent=75        # 75% CPU max
            ),
            'comprehensive': PerformanceBenchmark(
                operation_name='comprehensive',
                target_duration=15.0,     # 15 seconds max
                target_throughput=0.07,   # 0.07 workflows/second
                max_memory_mb=100,        # 100MB max increase
                max_cpu_percent=90        # 90% CPU max
            )
        }

    @pytest.mark.parametrize("workflow_type", ['shortage_analysis', 'supplier_risk', 'customer_priority'])
    @pytest.mark.asyncio
    async def test_single_workflow_execution_time(
        self,
        workflow_type: str,
        mock_orchestration_runner,
        performance_benchmarks: Dict[str, PerformanceBenchmark],
        performance_monitor,
        memory_usage_monitor
    ):
        """Measure and validate execution times for different workflow patterns."""
        benchmark = performance_benchmarks[workflow_type]
        
        memory_usage_monitor.start_monitoring()
        performance_monitor.start_timing(f'single_{workflow_type}')
        
        # Mock realistic execution times based on workflow type
        execution_times = {
            'shortage_analysis': 2.5,
            'supplier_risk': 3.8,
            'customer_priority': 2.1,
            'comprehensive': 8.5
        }
        
        # Mock workflow execution with realistic timing
        async def mock_timed_execution(query, workflow_type):
            # Simulate actual work with sleep
            base_time = execution_times.get(workflow_type, 3.0)
            await asyncio.sleep(base_time / 10)  # Scale down for testing
            
            return {
                'workflow_id': f'perf-test-{workflow_type}',
                'status': 'completed',
                'results': {
                    'mysql_results': {'orders': list(range(100))},  # Simulate data
                    'shortage_analysis': {'materials': list(range(50))},
                    'alerts_sent': list(range(10))
                },
                'execution_time': base_time,
                'performance_stats': {
                    'agent_times': {
                        'mysql': base_time * 0.4,
                        'shortage': base_time * 0.4,
                        'alert': base_time * 0.2
                    }
                }
            }
        
        mock_orchestration_runner.execute_financial_query.side_effect = mock_timed_execution
        
        # Execute workflow
        result = await mock_orchestration_runner.execute_financial_query(
            query=f"Test {workflow_type} performance",
            workflow_type=workflow_type
        )
        
        actual_time = performance_monitor.end_timing(f'single_{workflow_type}')
        memory_usage = memory_usage_monitor.get_memory_usage()
        
        # Validate execution time performance
        reported_time = result['execution_time']
        assert reported_time <= benchmark.target_duration, f"{workflow_type} took {reported_time}s, target: {benchmark.target_duration}s"
        
        # Validate memory usage
        assert memory_usage['increase_mb'] <= benchmark.max_memory_mb, f"Memory increase {memory_usage['increase_mb']}MB exceeds {benchmark.max_memory_mb}MB"
        
        # Validate result completeness (performance shouldn't compromise functionality)
        assert result['status'] == 'completed', "Workflow should complete successfully"
        assert 'performance_stats' in result, "Should include performance statistics"
        
        print(f"✓ {workflow_type}: {reported_time:.2f}s (target: {benchmark.target_duration}s), "
              f"memory: {memory_usage['increase_mb']:.1f}MB")

    @pytest.mark.asyncio
    async def test_comprehensive_workflow_performance(
        self,
        mock_orchestration_runner,
        performance_benchmarks: Dict[str, PerformanceBenchmark],
        performance_monitor,
        memory_usage_monitor
    ):
        """Test comprehensive workflow with all agents and complex processing."""
        benchmark = performance_benchmarks['comprehensive']
        
        memory_usage_monitor.start_monitoring()
        performance_monitor.start_timing('comprehensive_workflow')
        
        # Mock comprehensive workflow with realistic complexity
        async def mock_comprehensive_execution(query, workflow_type):
            # Simulate complex multi-agent processing
            stages = ['mysql', 'shortage', 'supplier', 'priority', 'alert']
            stage_times = [1.5, 2.0, 2.2, 1.8, 1.0]  # Different processing times
            
            total_time = 0
            stage_results = {}
            
            for i, (stage, stage_time) in enumerate(zip(stages, stage_times)):
                await asyncio.sleep(stage_time / 20)  # Scale down for testing
                total_time += stage_time
                
                # Simulate progressive data accumulation
                stage_results[f'{stage}_results'] = {
                    'data_size': (i + 1) * 50,
                    'processing_time': stage_time,
                    'items_processed': (i + 1) * 25
                }
            
            return {
                'workflow_id': 'comprehensive-perf-test',
                'status': 'completed',
                'results': stage_results,
                'execution_time': total_time,
                'performance_stats': {
                    'stage_times': dict(zip(stages, stage_times)),
                    'total_data_processed': sum(r['data_size'] for r in stage_results.values()),
                    'bottleneck_stage': max(zip(stages, stage_times), key=lambda x: x[1])[0]
                }
            }
        
        mock_orchestration_runner.execute_financial_query.side_effect = mock_comprehensive_execution
        
        # Execute comprehensive workflow
        result = await mock_orchestration_runner.execute_financial_query(
            query="Comprehensive financial risk analysis with all components",
            workflow_type='comprehensive'
        )
        
        actual_time = performance_monitor.end_timing('comprehensive_workflow')
        memory_usage = memory_usage_monitor.get_memory_usage()
        
        # Validate comprehensive performance
        execution_time = result['execution_time']
        assert execution_time <= benchmark.target_duration, f"Comprehensive workflow took {execution_time}s, target: {benchmark.target_duration}s"
        
        # Validate memory usage for complex workflow
        assert memory_usage['increase_mb'] <= benchmark.max_memory_mb, f"Memory usage {memory_usage['increase_mb']}MB exceeds {benchmark.max_memory_mb}MB"
        
        # Validate performance statistics
        perf_stats = result['performance_stats']
        assert 'stage_times' in perf_stats, "Should include individual stage timings"
        assert 'bottleneck_stage' in perf_stats, "Should identify performance bottleneck"
        
        # Validate data processing metrics
        total_data = perf_stats['total_data_processed']
        assert total_data > 0, "Should process actual data"
        
        print(f"✓ Comprehensive: {execution_time:.2f}s, memory: {memory_usage['increase_mb']:.1f}MB, "
              f"data processed: {total_data}, bottleneck: {perf_stats['bottleneck_stage']}")

    @pytest.mark.asyncio
    async def test_agent_level_performance_metrics(
        self,
        real_mysql_agent,
        mock_shortage_agent,
        mock_alert_agent,
        performance_monitor
    ):
        """Measure individual agent execution times and resource usage."""
        agent_performance = {}
        
        # Test MySQL agent performance
        performance_monitor.start_timing('mysql_agent')
        
        try:
            mysql_result = await real_mysql_agent.process_query(
                "SELECT COUNT(*) FROM orders WHERE status IN ('pending', 'processing')"
            )
            mysql_time = performance_monitor.end_timing('mysql_agent')
            
            agent_performance['mysql'] = {
                'execution_time': mysql_time,
                'success': mysql_result.get('status') == 'success',
                'data_processed': len(str(mysql_result.get('data', {})))
            }
        except Exception as e:
            mysql_time = performance_monitor.end_timing('mysql_agent')
            agent_performance['mysql'] = {
                'execution_time': mysql_time,
                'success': False,
                'error': str(e)
            }
        
        # Test shortage agent performance
        performance_monitor.start_timing('shortage_agent')
        
        shortage_result = await mock_shortage_agent.analyze_shortage({
            'materials': [{'code': f'MAT{i:03d}', 'stock': i * 10} for i in range(100)],
            'orders': [{'id': f'ORD{i:03d}', 'priority': 'HIGH'} for i in range(50)]
        })
        shortage_time = performance_monitor.end_timing('shortage_agent')
        
        agent_performance['shortage'] = {
            'execution_time': shortage_time,
            'success': shortage_result.get('status') == 'success',
            'materials_processed': 100,
            'orders_processed': 50
        }
        
        # Test alert agent performance
        performance_monitor.start_timing('alert_agent')
        
        alert_result = await mock_alert_agent.send_alerts({
            'alerts': [{'id': f'ALT{i:03d}', 'severity': 'HIGH'} for i in range(20)],
            'channels': ['email', 'slack']
        })
        alert_time = performance_monitor.end_timing('alert_agent')
        
        agent_performance['alert'] = {
            'execution_time': alert_time,
            'success': alert_result.get('status') == 'success',
            'alerts_processed': 20
        }
        
        # Validate agent performance targets
        performance_targets = {
            'mysql': 2.0,      # 2 seconds max
            'shortage': 3.0,   # 3 seconds max
            'alert': 1.0       # 1 second max
        }
        
        for agent, metrics in agent_performance.items():
            target_time = performance_targets[agent]
            actual_time = metrics['execution_time']
            
            assert actual_time <= target_time, f"{agent} agent took {actual_time:.2f}s, target: {target_time}s"
            
            if metrics['success']:
                print(f"✓ {agent} agent: {actual_time:.2f}s (target: {target_time}s)")
            else:
                print(f"? {agent} agent: {actual_time:.2f}s (failed: {metrics.get('error', 'unknown error')})")

    @pytest.mark.asyncio
    async def test_context_manager_performance(
        self,
        performance_monitor,
        memory_usage_monitor
    ):
        """Validate context creation, update, and retrieval performance."""
        context_manager = ContextManager()
        
        memory_usage_monitor.start_monitoring()
        performance_monitor.start_timing('context_operations')
        
        # Test context creation performance
        contexts = []
        for i in range(100):
            from ...src.orchestration.context_manager import WorkflowContext
            context = WorkflowContext(
                workflow_id=f'perf-test-{i:03d}',
                workflow_type='shortage_analysis',
                user_query=f'Performance test query {i}',
                entities={'materials': [f'MAT{i:03d}'], 'orders': [f'ORD{i:03d}']},
                metadata={'created_at': time.time(), 'test_data': 'x' * 1024}  # 1KB metadata
            )
            contexts.append(context)
        
        # Measure context storage performance
        performance_monitor.start_timing('context_storage')
        storage_tasks = [context_manager.store_context(ctx) for ctx in contexts]
        await asyncio.gather(*storage_tasks)
        storage_time = performance_monitor.end_timing('context_storage')
        
        # Measure context retrieval performance
        performance_monitor.start_timing('context_retrieval')
        retrieval_tasks = [context_manager.get_context(ctx.workflow_id) for ctx in contexts]
        retrieved_contexts = await asyncio.gather(*retrieval_tasks)
        retrieval_time = performance_monitor.end_timing('context_retrieval')
        
        # Measure context update performance
        performance_monitor.start_timing('context_updates')
        for i, context in enumerate(contexts):
            context.current_step = 'shortage_analysis'
            context.metadata['updated_at'] = time.time()
            context.metadata['update_count'] = i
            await context_manager.store_context(context)
        update_time = performance_monitor.end_timing('context_updates')
        
        total_time = performance_monitor.end_timing('context_operations')
        memory_usage = memory_usage_monitor.get_memory_usage()
        
        # Validate context performance
        context_count = len(contexts)
        
        # Storage performance
        storage_rate = context_count / storage_time
        assert storage_rate > 20, f"Context storage rate {storage_rate:.1f} contexts/s too slow"
        
        # Retrieval performance
        retrieval_rate = context_count / retrieval_time
        assert retrieval_rate > 50, f"Context retrieval rate {retrieval_rate:.1f} contexts/s too slow"
        
        # Update performance
        update_rate = context_count / update_time
        assert update_rate > 30, f"Context update rate {update_rate:.1f} contexts/s too slow"
        
        # Memory efficiency
        expected_memory_mb = (context_count * 1) / 1024  # 1KB per context
        actual_memory_mb = memory_usage['increase_mb']
        memory_efficiency = expected_memory_mb / actual_memory_mb if actual_memory_mb > 0 else 1
        
        assert memory_efficiency > 0.1, f"Memory efficiency too low: {memory_efficiency:.2f}"
        
        # Validate retrieved contexts
        assert len(retrieved_contexts) == context_count, "Should retrieve all stored contexts"
        for retrieved in retrieved_contexts:
            assert retrieved is not None, "All contexts should be retrievable"
        
        print(f"✓ Context performance: {context_count} contexts, "
              f"storage: {storage_rate:.1f}/s, retrieval: {retrieval_rate:.1f}/s, "
              f"updates: {update_rate:.1f}/s, memory: {actual_memory_mb:.1f}MB")


class TestConcurrentWorkflowHandling:
    """Test system performance under concurrent workflow load."""

    @pytest.fixture
    def concurrency_test_scenarios(self):
        """Define concurrency test scenarios."""
        return [
            {
                'name': 'light_load',
                'concurrent_workflows': 3,
                'workflow_types': ['shortage_analysis', 'supplier_risk', 'customer_priority'],
                'expected_completion_time': 10.0,
                'memory_limit_mb': 150
            },
            {
                'name': 'medium_load',
                'concurrent_workflows': 5,
                'workflow_types': ['shortage_analysis', 'supplier_risk', 'customer_priority', 'comprehensive', 'shortage_analysis'],
                'expected_completion_time': 15.0,
                'memory_limit_mb': 250
            },
            {
                'name': 'heavy_load',
                'concurrent_workflows': 8,
                'workflow_types': ['shortage_analysis'] * 4 + ['comprehensive'] * 2 + ['supplier_risk'] * 2,
                'expected_completion_time': 25.0,
                'memory_limit_mb': 400
            }
        ]

    @pytest.mark.parametrize("scenario_name", ['light_load', 'medium_load'])
    @pytest.mark.asyncio
    async def test_concurrent_workflow_performance(
        self,
        scenario_name: str,
        concurrency_test_scenarios: List[Dict[str, Any]],
        mock_orchestration_runner,
        performance_monitor,
        memory_usage_monitor
    ):
        """Execute multiple workflows concurrently and measure performance impact."""
        scenario = next(s for s in concurrency_test_scenarios if s['name'] == scenario_name)
        
        memory_usage_monitor.start_monitoring()
        performance_monitor.start_timing(f'concurrent_{scenario_name}')
        
        # Mock concurrent workflow execution
        async def mock_concurrent_execution(query, workflow_type, workflow_id):
            # Simulate different execution times for different workflow types
            execution_times = {
                'shortage_analysis': 2.5,
                'supplier_risk': 3.8,
                'customer_priority': 2.1,
                'comprehensive': 8.5
            }
            
            base_time = execution_times.get(workflow_type, 3.0)
            # Add some randomness to simulate real-world variance
            import random
            actual_time = base_time * (0.8 + 0.4 * random.random())
            
            await asyncio.sleep(actual_time / 10)  # Scale down for testing
            
            return {
                'workflow_id': workflow_id,
                'workflow_type': workflow_type,
                'status': 'completed',
                'execution_time': actual_time,
                'results': {'simulated': True},
                'concurrency_info': {
                    'scenario': scenario_name,
                    'concurrent_count': scenario['concurrent_workflows']
                }
            }
        
        # Create concurrent workflow tasks
        workflow_tasks = []
        for i, workflow_type in enumerate(scenario['workflow_types']):
            workflow_id = f'{scenario_name}-{i:02d}'
            task = mock_concurrent_execution(
                query=f"Concurrent test query {i}",
                workflow_type=workflow_type,
                workflow_id=workflow_id
            )
            workflow_tasks.append(task)
        
        # Execute all workflows concurrently
        start_time = time.time()
        results = await asyncio.gather(*workflow_tasks)
        end_time = time.time()
        
        concurrent_time = performance_monitor.end_timing(f'concurrent_{scenario_name}')
        memory_usage = memory_usage_monitor.get_memory_usage()
        
        # Validate concurrent execution performance
        total_workflows = len(results)
        assert total_workflows == scenario['concurrent_workflows'], f"Should complete all {scenario['concurrent_workflows']} workflows"
        
        # Validate completion time
        actual_completion_time = end_time - start_time
        assert actual_completion_time <= scenario['expected_completion_time'], f"Concurrent execution took {actual_completion_time:.2f}s, expected: {scenario['expected_completion_time']}s"
        
        # Validate memory usage
        assert memory_usage['increase_mb'] <= scenario['memory_limit_mb'], f"Memory usage {memory_usage['increase_mb']:.1f}MB exceeds limit {scenario['memory_limit_mb']}MB"
        
        # Validate all workflows completed successfully
        successful_workflows = [r for r in results if r['status'] == 'completed']
        assert len(successful_workflows) == total_workflows, "All concurrent workflows should complete successfully"
        
        # Calculate performance metrics
        total_execution_time = sum(r['execution_time'] for r in results)
        efficiency = total_execution_time / actual_completion_time if actual_completion_time > 0 else 0
        throughput = total_workflows / actual_completion_time
        
        print(f"✓ {scenario_name}: {total_workflows} workflows, "
              f"completion: {actual_completion_time:.2f}s, efficiency: {efficiency:.2f}x, "
              f"throughput: {throughput:.2f} workflows/s, memory: {memory_usage['increase_mb']:.1f}MB")

    @pytest.mark.asyncio
    async def test_max_concurrent_workflows_limit(
        self,
        mock_orchestration_runner,
        performance_monitor
    ):
        """Test max_concurrent_workflows configuration limits."""
        max_concurrent = 4
        total_workflows = 10
        
        performance_monitor.start_timing('concurrent_limit_test')
        
        # Track concurrent execution
        currently_executing = 0
        max_concurrent_observed = 0
        execution_log = []
        
        async def mock_limited_execution(query, workflow_type, workflow_id):
            nonlocal currently_executing, max_concurrent_observed
            
            currently_executing += 1
            max_concurrent_observed = max(max_concurrent_observed, currently_executing)
            
            start_time = time.time()
            execution_log.append(('start', workflow_id, start_time, currently_executing))
            
            # Simulate work
            await asyncio.sleep(0.5)  # 500ms per workflow
            
            currently_executing -= 1
            end_time = time.time()
            execution_log.append(('end', workflow_id, end_time, currently_executing))
            
            return {
                'workflow_id': workflow_id,
                'status': 'completed',
                'execution_time': end_time - start_time
            }
        
        # Use semaphore to limit concurrency
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def execute_with_limit(workflow_id):
            async with semaphore:
                return await mock_limited_execution(
                    query=f"Limited execution test {workflow_id}",
                    workflow_type="shortage_analysis",
                    workflow_id=workflow_id
                )
        
        # Execute workflows with concurrency limit
        tasks = [execute_with_limit(f'limit-test-{i:02d}') for i in range(total_workflows)]
        results = await asyncio.gather(*tasks)
        
        limit_test_time = performance_monitor.end_timing('concurrent_limit_test')
        
        # Validate concurrency limit was respected
        assert max_concurrent_observed <= max_concurrent, f"Exceeded concurrent limit: observed {max_concurrent_observed}, limit {max_concurrent}"
        
        # Validate all workflows completed
        assert len(results) == total_workflows, f"Should complete all {total_workflows} workflows"
        successful_results = [r for r in results if r['status'] == 'completed']
        assert len(successful_results) == total_workflows, "All workflows should complete successfully"
        
        # Analyze execution pattern
        start_times = [log for log in execution_log if log[0] == 'start']
        end_times = [log for log in execution_log if log[0] == 'end']
        
        # Validate that workflows were queued properly
        expected_min_time = (total_workflows / max_concurrent) * 0.5  # Theoretical minimum time
        assert limit_test_time >= expected_min_time * 0.8, f"Execution too fast, may not be respecting limits: {limit_test_time:.2f}s"
        
        print(f"✓ Concurrency limit: {total_workflows} workflows, max concurrent: {max_concurrent}, "
              f"observed max: {max_concurrent_observed}, total time: {limit_test_time:.2f}s")

    @pytest.mark.asyncio
    async def test_workflow_priority_handling(
        self,
        mock_orchestration_runner,
        performance_monitor
    ):
        """Test workflow prioritization in concurrent execution."""
        performance_monitor.start_timing('priority_handling')
        
        # Define workflows with priorities
        workflows = [
            {'id': 'high-1', 'priority': 'HIGH', 'type': 'shortage_analysis'},
            {'id': 'low-1', 'priority': 'LOW', 'type': 'supplier_risk'},
            {'id': 'high-2', 'priority': 'HIGH', 'type': 'customer_priority'},
            {'id': 'medium-1', 'priority': 'MEDIUM', 'type': 'shortage_analysis'},
            {'id': 'low-2', 'priority': 'LOW', 'type': 'comprehensive'},
            {'id': 'high-3', 'priority': 'HIGH', 'type': 'shortage_analysis'}
        ]
        
        execution_order = []
        
        async def mock_priority_execution(workflow):
            # Record execution order
            start_time = time.time()
            execution_order.append({
                'workflow_id': workflow['id'],
                'priority': workflow['priority'],
                'start_time': start_time
            })
            
            # Simulate execution time based on priority (higher priority = faster)
            priority_multipliers = {'HIGH': 0.5, 'MEDIUM': 0.75, 'LOW': 1.0}
            base_time = 1.0
            execution_time = base_time * priority_multipliers[workflow['priority']]
            
            await asyncio.sleep(execution_time / 10)  # Scale down
            
            return {
                'workflow_id': workflow['id'],
                'priority': workflow['priority'],
                'status': 'completed',
                'execution_time': execution_time,
                'start_time': start_time
            }
        
        # Execute workflows with priority scheduling
        # Group by priority and execute high priority first
        high_priority_tasks = [mock_priority_execution(w) for w in workflows if w['priority'] == 'HIGH']
        medium_priority_tasks = [mock_priority_execution(w) for w in workflows if w['priority'] == 'MEDIUM']
        low_priority_tasks = [mock_priority_execution(w) for w in workflows if w['priority'] == 'LOW']
        
        # Execute in priority order
        high_results = await asyncio.gather(*high_priority_tasks) if high_priority_tasks else []
        medium_results = await asyncio.gather(*medium_priority_tasks) if medium_priority_tasks else []
        low_results = await asyncio.gather(*low_priority_tasks) if low_priority_tasks else []
        
        all_results = high_results + medium_results + low_results
        
        priority_time = performance_monitor.end_timing('priority_handling')
        
        # Validate priority execution order
        high_priority_workflows = [w for w in workflows if w['priority'] == 'HIGH']
        high_priority_results = [r for r in all_results if r['priority'] == 'HIGH']
        
        assert len(high_priority_results) == len(high_priority_workflows), "Should execute all high priority workflows"
        
        # Validate that high priority workflows started first
        if len(execution_order) > 0:
            first_batch = execution_order[:len(high_priority_workflows)]
            for exec_info in first_batch:
                assert exec_info['priority'] == 'HIGH', f"First executing workflow should be high priority, got {exec_info['priority']}"
        
        # Validate execution time benefits for high priority
        if high_priority_results and low_priority_tasks:
            avg_high_time = sum(r['execution_time'] for r in high_priority_results) / len(high_priority_results)
            low_priority_results = [r for r in all_results if r['priority'] == 'LOW']
            if low_priority_results:
                avg_low_time = sum(r['execution_time'] for r in low_priority_results) / len(low_priority_results)
                assert avg_high_time < avg_low_time, "High priority workflows should execute faster"
        
        print(f"✓ Priority handling: {len(all_results)} workflows, "
              f"HIGH: {len(high_priority_results)}, "
              f"MEDIUM: {len([r for r in all_results if r['priority'] == 'MEDIUM'])}, "
              f"LOW: {len([r for r in all_results if r['priority'] == 'LOW'])}")


class TestMemoryAndResourceManagement:
    """Monitor memory usage and resource management."""

    @pytest.mark.asyncio
    async def test_memory_usage_under_load(
        self,
        mock_orchestration_runner,
        memory_usage_monitor,
        performance_monitor,
        large_dataset_generator
    ):
        """Monitor memory usage during high-load scenarios."""
        memory_usage_monitor.start_monitoring()
        performance_monitor.start_timing('memory_load_test')
        
        # Generate large datasets for testing
        large_orders = large_dataset_generator['orders'](1000)  # 1000 orders
        large_materials = large_dataset_generator['materials'](500)  # 500 materials
        large_suppliers = large_dataset_generator['suppliers'](100)  # 100 suppliers
        
        # Mock memory-intensive workflow execution
        async def mock_memory_intensive_execution(dataset_size):
            # Simulate memory allocation for large datasets
            processing_data = {
                'orders': large_orders[:dataset_size],
                'materials': large_materials[:dataset_size // 2],
                'suppliers': large_suppliers[:dataset_size // 10],
                'analysis_cache': ['x' * 1024] * dataset_size,  # 1KB per item
                'temp_calculations': list(range(dataset_size * 100))
            }
            
            await asyncio.sleep(0.1)  # Simulate processing time
            
            # Force memory usage tracking
            memory_usage_monitor.update_peak()
            
            return {
                'workflow_id': f'memory-test-{dataset_size}',
                'status': 'completed',
                'dataset_size': dataset_size,
                'data_processed_mb': len(str(processing_data)) / (1024 * 1024)
            }
        
        # Test with increasing dataset sizes
        dataset_sizes = [100, 200, 300, 400, 500]
        memory_results = []
        
        for size in dataset_sizes:
            gc.collect()  # Force garbage collection before each test
            current_memory = memory_usage_monitor.get_memory_usage()
            
            result = await mock_memory_intensive_execution(size)
            
            post_execution_memory = memory_usage_monitor.get_memory_usage()
            memory_increase = post_execution_memory['current_mb'] - current_memory['current_mb']
            
            memory_results.append({
                'dataset_size': size,
                'memory_increase_mb': memory_increase,
                'data_processed_mb': result['data_processed_mb']
            })
        
        load_test_time = performance_monitor.end_timing('memory_load_test')
        final_memory = memory_usage_monitor.get_memory_usage()
        
        # Validate memory usage scaling
        for i, result in enumerate(memory_results):
            dataset_size = result['dataset_size']
            memory_increase = result['memory_increase_mb']
            
            # Memory usage should scale reasonably with dataset size
            expected_memory_mb = dataset_size * 0.001  # ~1KB per item
            memory_efficiency = expected_memory_mb / memory_increase if memory_increase > 0 else 1
            
            assert memory_efficiency > 0.01, f"Memory usage too high for dataset {dataset_size}: {memory_increase:.1f}MB"
            
            print(f"Dataset {dataset_size}: {memory_increase:.1f}MB, "
                  f"efficiency: {memory_efficiency:.3f}")
        
        # Validate total memory increase is reasonable
        assert final_memory['increase_mb'] < 500, f"Total memory increase {final_memory['increase_mb']:.1f}MB too high"

    @pytest.mark.asyncio
    async def test_memory_leak_detection(
        self,
        mock_orchestration_runner,
        memory_usage_monitor,
        performance_monitor
    ):
        """Test for memory leaks during repeated workflow executions."""
        memory_usage_monitor.start_monitoring()
        performance_monitor.start_timing('memory_leak_test')
        
        initial_memory = memory_usage_monitor.get_memory_usage()
        memory_measurements = []
        
        # Mock workflow execution that could potentially leak memory
        async def mock_workflow_execution(iteration):
            # Simulate workflow with temporary data structures
            temp_data = {
                'iteration': iteration,
                'large_temp_data': ['x' * 1024] * 100,  # 100KB temporary data
                'calculations': list(range(1000)),
                'cache': {f'key_{i}': f'value_{i}' * 10 for i in range(100)}
            }
            
            await asyncio.sleep(0.01)  # Simulate processing
            
            return {
                'workflow_id': f'leak-test-{iteration}',
                'status': 'completed',
                'iteration': iteration
            }
        
        # Execute workflows in batches to detect memory leaks
        batch_size = 10
        num_batches = 5
        
        for batch in range(num_batches):
            # Execute batch of workflows
            batch_tasks = [mock_workflow_execution(i) for i in range(batch * batch_size, (batch + 1) * batch_size)]
            await asyncio.gather(*batch_tasks)
            
            # Force garbage collection
            gc.collect()
            await asyncio.sleep(0.1)  # Allow cleanup
            
            # Measure memory usage
            current_memory = memory_usage_monitor.get_memory_usage()
            memory_measurements.append({
                'batch': batch,
                'workflows_executed': (batch + 1) * batch_size,
                'memory_mb': current_memory['current_mb'],
                'memory_increase_mb': current_memory['current_mb'] - initial_memory['current_mb']
            })
        
        leak_test_time = performance_monitor.end_timing('memory_leak_test')
        
        # Analyze memory usage trend
        memory_increases = [m['memory_increase_mb'] for m in memory_measurements]
        
        # Check if memory usage keeps increasing (potential leak)
        if len(memory_increases) >= 3:
            # Calculate trend
            early_memory = sum(memory_increases[:2]) / 2
            late_memory = sum(memory_increases[-2:]) / 2
            
            memory_growth_rate = (late_memory - early_memory) / early_memory if early_memory > 0 else 0
            
            # Memory growth should be minimal after initial allocation
            assert memory_growth_rate < 0.5, f"Potential memory leak detected: growth rate {memory_growth_rate:.2f}"
        
        # Validate final memory usage
        final_memory = memory_usage_monitor.get_memory_usage()
        total_workflows = num_batches * batch_size
        
        # Memory per workflow should be reasonable
        memory_per_workflow = final_memory['increase_mb'] / total_workflows if total_workflows > 0 else 0
        assert memory_per_workflow < 2.0, f"Memory per workflow too high: {memory_per_workflow:.2f}MB"
        
        print(f"✓ Memory leak test: {total_workflows} workflows, "
              f"final memory: {final_memory['increase_mb']:.1f}MB, "
              f"per workflow: {memory_per_workflow:.3f}MB")

    @pytest.mark.asyncio
    async def test_resource_cleanup_verification(
        self,
        performance_monitor
    ):
        """Test proper resource cleanup after workflow completion."""
        performance_monitor.start_timing('resource_cleanup_test')
        
        # Track resource usage
        resources_created = []
        resources_cleaned = []
        
        class MockResource:
            def __init__(self, resource_id):
                self.resource_id = resource_id
                self.is_cleaned = False
                resources_created.append(self)
            
            async def cleanup(self):
                self.is_cleaned = True
                resources_cleaned.append(self)
        
        async def mock_workflow_with_resources(workflow_id):
            # Create mock resources during workflow execution
            db_connection = MockResource(f'{workflow_id}-db')
            cache_manager = MockResource(f'{workflow_id}-cache')
            temp_files = [MockResource(f'{workflow_id}-temp-{i}') for i in range(3)]
            
            try:
                # Simulate workflow execution
                await asyncio.sleep(0.05)
                
                return {
                    'workflow_id': workflow_id,
                    'status': 'completed',
                    'resources_used': len(temp_files) + 2
                }
            
            finally:
                # Cleanup resources
                await db_connection.cleanup()
                await cache_manager.cleanup()
                for temp_file in temp_files:
                    await temp_file.cleanup()
        
        # Execute workflows with resources
        workflow_count = 20
        tasks = [mock_workflow_with_resources(f'cleanup-test-{i:02d}') for i in range(workflow_count)]
        results = await asyncio.gather(*tasks)
        
        cleanup_time = performance_monitor.end_timing('resource_cleanup_test')
        
        # Validate resource cleanup
        total_resources_created = len(resources_created)
        total_resources_cleaned = len(resources_cleaned)
        
        assert total_resources_created == total_resources_cleaned, f"Resource leak: created {total_resources_created}, cleaned {total_resources_cleaned}"
        
        # Validate all resources were properly cleaned
        for resource in resources_created:
            assert resource.is_cleaned, f"Resource {resource.resource_id} was not cleaned up"
        
        # Validate workflow completion
        successful_workflows = [r for r in results if r['status'] == 'completed']
        assert len(successful_workflows) == workflow_count, "All workflows should complete successfully"
        
        # Calculate resource efficiency
        expected_resources = workflow_count * 5  # 2 + 3 resources per workflow
        assert total_resources_created == expected_resources, f"Expected {expected_resources} resources, got {total_resources_created}"
        
        print(f"✓ Resource cleanup: {workflow_count} workflows, "
              f"{total_resources_created} resources created/cleaned, "
              f"cleanup time: {cleanup_time:.2f}s")


class TestScalabilityValidation:
    """Test system scalability with increasing load."""

    @pytest.fixture
    def scalability_test_levels(self):
        """Define scalability test levels."""
        return [
            {'name': 'baseline', 'workflows': 2, 'target_time': 5.0, 'target_memory': 50},
            {'name': 'scale_2x', 'workflows': 4, 'target_time': 8.0, 'target_memory': 100},
            {'name': 'scale_4x', 'workflows': 8, 'target_time': 15.0, 'target_memory': 200},
            {'name': 'scale_8x', 'workflows': 16, 'target_time': 30.0, 'target_memory': 400}
        ]

    @pytest.mark.parametrize("scale_level", ['baseline', 'scale_2x', 'scale_4x'])
    @pytest.mark.asyncio
    async def test_system_scalability_with_increasing_load(
        self,
        scale_level: str,
        scalability_test_levels: List[Dict[str, Any]],
        mock_orchestration_runner,
        performance_monitor,
        memory_usage_monitor
    ):
        """Test system scalability with increasing load."""
        level = next(l for l in scalability_test_levels if l['name'] == scale_level)
        
        memory_usage_monitor.start_monitoring()
        performance_monitor.start_timing(f'scalability_{scale_level}')
        
        # Mock scalable workflow execution
        async def mock_scalable_execution(workflow_id, load_factor):
            # Execution time scales with load due to resource contention
            base_time = 2.0
            scaled_time = base_time * (1 + 0.1 * load_factor)  # 10% increase per 2x load
            
            await asyncio.sleep(scaled_time / 20)  # Scale down for testing
            
            return {
                'workflow_id': workflow_id,
                'status': 'completed',
                'execution_time': scaled_time,
                'load_factor': load_factor
            }
        
        # Calculate load factor based on workflow count
        baseline_workflows = 2
        load_factor = level['workflows'] / baseline_workflows
        
        # Execute workflows at scale
        tasks = [
            mock_scalable_execution(f'{scale_level}-{i:02d}', load_factor) 
            for i in range(level['workflows'])
        ]
        
        results = await asyncio.gather(*tasks)
        
        scalability_time = performance_monitor.end_timing(f'scalability_{scale_level}')
        memory_usage = memory_usage_monitor.get_memory_usage()
        
        # Validate scalability metrics
        assert len(results) == level['workflows'], f"Should complete all {level['workflows']} workflows"
        
        # Validate completion time scaling
        assert scalability_time <= level['target_time'], f"Scalability test took {scalability_time:.2f}s, target: {level['target_time']}s"
        
        # Validate memory scaling
        assert memory_usage['increase_mb'] <= level['target_memory'], f"Memory usage {memory_usage['increase_mb']:.1f}MB exceeds target {level['target_memory']}MB"
        
        # Calculate efficiency metrics
        avg_execution_time = sum(r['execution_time'] for r in results) / len(results)
        throughput = level['workflows'] / scalability_time
        
        # Efficiency should not degrade too much with scale
        baseline_throughput = 2 / 5.0  # baseline: 2 workflows in 5 seconds
        efficiency_ratio = throughput / baseline_throughput
        
        expected_min_efficiency = 1 / load_factor * 0.7  # Allow 30% degradation
        assert efficiency_ratio >= expected_min_efficiency, f"Efficiency degraded too much: {efficiency_ratio:.2f}, expected: {expected_min_efficiency:.2f}"
        
        print(f"✓ {scale_level}: {level['workflows']} workflows, "
              f"time: {scalability_time:.2f}s, throughput: {throughput:.2f}/s, "
              f"efficiency: {efficiency_ratio:.2f}x, memory: {memory_usage['increase_mb']:.1f}MB")


class TestPerformanceRegression:
    """Establish performance baselines and detect regressions."""

    @pytest.fixture
    def performance_baselines(self):
        """Define performance baselines for regression testing."""
        return {
            'single_workflow_shortage': {'max_time': 5.0, 'max_memory': 50},
            'single_workflow_comprehensive': {'max_time': 15.0, 'max_memory': 100},
            'concurrent_3_workflows': {'max_time': 10.0, 'max_memory': 150},
            'context_operations_100': {'max_time': 2.0, 'max_memory': 20},
            'agent_mysql_query': {'max_time': 3.0, 'max_memory': 30}
        }

    @pytest.mark.asyncio
    async def test_performance_baseline_validation(
        self,
        performance_baselines: Dict[str, Dict[str, float]],
        mock_orchestration_runner,
        performance_monitor,
        memory_usage_monitor
    ):
        """Test current performance against established baselines."""
        baseline_results = {}
        
        # Test single workflow performance
        for workflow_type, baseline_key in [
            ('shortage_analysis', 'single_workflow_shortage'),
            ('comprehensive', 'single_workflow_comprehensive')
        ]:
            if baseline_key in performance_baselines:
                baseline = performance_baselines[baseline_key]
                
                memory_usage_monitor.start_monitoring()
                performance_monitor.start_timing(baseline_key)
                
                # Mock workflow execution
                async def mock_baseline_execution():
                    execution_times = {'shortage_analysis': 2.5, 'comprehensive': 8.5}
                    base_time = execution_times[workflow_type]
                    await asyncio.sleep(base_time / 20)
                    return {'status': 'completed', 'execution_time': base_time}
                
                result = await mock_baseline_execution()
                actual_time = performance_monitor.end_timing(baseline_key)
                memory_usage = memory_usage_monitor.get_memory_usage()
                
                # Validate against baseline
                assert actual_time <= baseline['max_time'], f"{baseline_key} exceeded time baseline: {actual_time:.2f}s > {baseline['max_time']}s"
                assert memory_usage['increase_mb'] <= baseline['max_memory'], f"{baseline_key} exceeded memory baseline: {memory_usage['increase_mb']:.1f}MB > {baseline['max_memory']}MB"
                
                baseline_results[baseline_key] = {
                    'actual_time': actual_time,
                    'actual_memory': memory_usage['increase_mb'],
                    'time_ratio': actual_time / baseline['max_time'],
                    'memory_ratio': memory_usage['increase_mb'] / baseline['max_memory']
                }
        
        # Test concurrent workflow performance
        if 'concurrent_3_workflows' in performance_baselines:
            baseline = performance_baselines['concurrent_3_workflows']
            
            memory_usage_monitor.start_monitoring()
            performance_monitor.start_timing('concurrent_3_workflows')
            
            # Mock concurrent execution
            tasks = [
                asyncio.sleep(0.1) for _ in range(3)  # 3 concurrent workflows
            ]
            await asyncio.gather(*tasks)
            
            concurrent_time = performance_monitor.end_timing('concurrent_3_workflows')
            memory_usage = memory_usage_monitor.get_memory_usage()
            
            assert concurrent_time <= baseline['max_time'], f"Concurrent workflows exceeded baseline: {concurrent_time:.2f}s > {baseline['max_time']}s"
            assert memory_usage['increase_mb'] <= baseline['max_memory'], f"Concurrent memory exceeded baseline: {memory_usage['increase_mb']:.1f}MB > {baseline['max_memory']}MB"
            
            baseline_results['concurrent_3_workflows'] = {
                'actual_time': concurrent_time,
                'actual_memory': memory_usage['increase_mb'],
                'time_ratio': concurrent_time / baseline['max_time'],
                'memory_ratio': memory_usage['increase_mb'] / baseline['max_memory']
            }
        
        # Generate performance report
        print("Performance Baseline Validation:")
        for test_name, result in baseline_results.items():
            time_status = "PASS" if result['time_ratio'] <= 1.0 else "FAIL"
            memory_status = "PASS" if result['memory_ratio'] <= 1.0 else "FAIL"
            
            print(f"  {test_name}: Time {time_status} ({result['time_ratio']:.2f}x), "
                  f"Memory {memory_status} ({result['memory_ratio']:.2f}x)")

    @pytest.mark.asyncio
    async def test_performance_trend_analysis(
        self,
        mock_orchestration_runner,
        performance_monitor
    ):
        """Analyze performance trends to detect gradual regressions."""
        # Simulate multiple test runs to establish trend
        test_runs = 10
        performance_data = []
        
        for run in range(test_runs):
            performance_monitor.start_timing(f'trend_test_run_{run}')
            
            # Mock workflow execution with slight variance
            import random
            base_time = 2.5
            variance = random.uniform(0.8, 1.2)  # ±20% variance
            execution_time = base_time * variance
            
            await asyncio.sleep(execution_time / 50)  # Scale down
            
            actual_time = performance_monitor.end_timing(f'trend_test_run_{run}')
            
            performance_data.append({
                'run': run,
                'execution_time': execution_time,
                'actual_time': actual_time,
                'variance': variance
            })
        
        # Analyze performance trend
        execution_times = [d['execution_time'] for d in performance_data]
        
        # Calculate trend metrics
        avg_time = sum(execution_times) / len(execution_times)
        min_time = min(execution_times)
        max_time = max(execution_times)
        std_deviation = (sum((t - avg_time) ** 2 for t in execution_times) / len(execution_times)) ** 0.5
        
        # Calculate trend (simple linear regression)
        n = len(execution_times)
        sum_x = sum(range(n))
        sum_y = sum(execution_times)
        sum_xy = sum(i * execution_times[i] for i in range(n))
        sum_x2 = sum(i ** 2 for i in range(n))
        
        if n * sum_x2 - sum_x ** 2 != 0:
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)
        else:
            slope = 0
        
        # Validate performance stability
        coefficient_of_variation = std_deviation / avg_time if avg_time > 0 else 0
        assert coefficient_of_variation < 0.3, f"Performance too variable: CV={coefficient_of_variation:.3f}"
        
        # Validate trend (no significant regression)
        max_acceptable_slope = 0.05  # 5% increase per run would be concerning
        assert slope <= max_acceptable_slope, f"Performance regression detected: slope={slope:.4f}"
        
        print(f"✓ Performance trend: avg={avg_time:.2f}s, std={std_deviation:.3f}s, "
              f"CV={coefficient_of_variation:.3f}, trend={slope:.4f}/run")