"""Comprehensive performance and load testing for the orchestration system.

Tests workflow execution performance, concurrent workflow handling, memory and resource management,
scalability validation, and performance regression detection.
"""

import asyncio
import time
import gc
import pytest
import pytest_asyncio
from typing import Dict, Any, List, Optional, Tuple

# Add the parent directory to sys.path for imports
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from unittest.mock import AsyncMock, patch
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

from orchestrator.orchestration_runner import OrchestrationRunner
from orchestrator.financial_orchestrator import FinancialOrchestrator
from orchestrator.context_manager import ContextManager
from orchestrator.workflow_patterns import WorkflowPattern, ExecutionMode


@dataclass
class PerformanceBenchmark:
    """Performance benchmark data structure."""
    operation_name: str
    target_duration: float
    target_throughput: float
    max_memory_mb: float
    max_cpu_percent: float


class TestWorkflowExecutionPerformance:
    """Validate execution times and performance benchmarks."""

    @pytest.fixture
    def performance_benchmarks(self):
        """Define performance benchmarks for different workflow types."""
        return {
            'shortage_analysis': PerformanceBenchmark(
                operation_name='shortage_analysis',
                target_duration=5.0,      # 5 seconds max
                target_throughput=0.2,    # 0.2 workflows/second
                max_memory_mb=50,         # 50MB max increase
                max_cpu_percent=80        # 80% CPU max
            ),
            'supplier_risk': PerformanceBenchmark(
                operation_name='supplier_risk',
                target_duration=7.0,      # 7 seconds max
                target_throughput=0.15,   # 0.15 workflows/second
                max_memory_mb=60,         # 60MB max increase
                max_cpu_percent=85        # 85% CPU max
            ),
            'customer_priority': PerformanceBenchmark(
                operation_name='customer_priority',
                target_duration=4.0,      # 4 seconds max
                target_throughput=0.25,   # 0.25 workflows/second
                max_memory_mb=40,         # 40MB max increase
                max_cpu_percent=75        # 75% CPU max
            ),
            'comprehensive': PerformanceBenchmark(
                operation_name='comprehensive',
                target_duration=15.0,     # 15 seconds max
                target_throughput=0.07,   # 0.07 workflows/second
                max_memory_mb=100,        # 100MB max increase
                max_cpu_percent=90        # 90% CPU max
            )
        }

    @pytest.mark.parametrize("workflow_type", ['shortage_analysis', 'supplier_risk', 'customer_priority'])
    @pytest.mark.asyncio
    async def test_single_workflow_execution_time(
        self,
        workflow_type: str,
        mock_orchestration_runner,
        performance_benchmarks: Dict[str, PerformanceBenchmark],
        performance_monitor,
        memory_usage_monitor
    ):
        """Measure and validate execution times for different workflow patterns."""
        benchmark = performance_benchmarks[workflow_type]
        
        memory_usage_monitor.start_monitoring()
        performance_monitor.start_timing(f'single_{workflow_type}')
        
        # Mock realistic execution times based on workflow type
        execution_times = {
            'shortage_analysis': 2.5,
            'supplier_risk': 3.8,
            'customer_priority': 2.1,
            'comprehensive': 8.5
        }
        
        # Mock workflow execution with realistic timing
        async def mock_timed_execution(query, workflow_type):
            # Simulate actual work with sleep
            base_time = execution_times.get(workflow_type, 3.0)
            await asyncio.sleep(base_time / 10)  # Scale down for testing
            
            return {
                'workflow_id': f'perf-test-{workflow_type}',
                'status': 'completed',
                'results': {
                    'mysql_results': {'orders': list(range(100))},  # Simulate data
                    'shortage_analysis': {'materials': list(range(50))},
                    'alerts_sent': list(range(10))
                },
                'execution_time': base_time,
                'performance_stats': {
                    'agent_times': {
                        'mysql': base_time * 0.4,
                        'shortage': base_time * 0.4,
                        'alert': base_time * 0.2
                    }
                }
            }
        
        mock_orchestration_runner.execute_financial_query.side_effect = mock_timed_execution
        
        # Execute workflow
        result = await mock_orchestration_runner.execute_financial_query(
            query=f"Test {workflow_type} performance",
            workflow_type=workflow_type
        )
        
        actual_time = performance_monitor.end_timing(f'single_{workflow_type}')
        memory_usage = memory_usage_monitor.get_memory_usage()
        
        # Validate execution time performance
        reported_time = result['execution_time']
        assert reported_time <= benchmark.target_duration, f"{workflow_type} took {reported_time}s, target: {benchmark.target_duration}s"
        
        # Validate memory usage
        assert memory_usage['increase_mb'] <= benchmark.max_memory_mb, f"Memory increase {memory_usage['increase_mb']}MB exceeds {benchmark.max_memory_mb}MB"
        
        # Validate result completeness (performance shouldn't compromise functionality)
        assert result['status'] == 'completed', "Workflow should complete successfully"
        assert 'performance_stats' in result, "Should include performance statistics"
        
        print(f"✓ {workflow_type}: {reported_time:.2f}s (target: {benchmark.target_duration}s), "
              f"memory: {memory_usage['increase_mb']:.1f}MB")

    @pytest.mark.asyncio
    async def test_context_manager_performance(
        self,
        performance_monitor,
        memory_usage_monitor
    ):
        """Validate context creation, update, and retrieval performance."""
        context_manager = ContextManager()
        
        memory_usage_monitor.start_monitoring()
        performance_monitor.start_timing('context_operations')
        
        # Test context creation performance - FIXED IMPORT
        contexts = []
        for i in range(100):
            from orchestrator.context_manager import FinancialWorkflowContext as WorkflowContext
            context = WorkflowContext(
                workflow_id=f'perf-test-{i:03d}',
                workflow_type='shortage_analysis',
                user_query=f'Performance test query {i}',
                entities={'materials': [f'MAT{i:03d}'], 'orders': [f'ORD{i:03d}']},
                metadata={'created_at': time.time(), 'test_data': 'x' * 1024}  # 1KB metadata
            )
            contexts.append(context)
        
        # Measure context storage performance
        performance_monitor.start_timing('context_storage')
        storage_tasks = [context_manager.store_context(ctx) for ctx in contexts]
        await asyncio.gather(*storage_tasks)
        storage_time = performance_monitor.end_timing('context_storage')
        
        # Measure context retrieval performance
        performance_monitor.start_timing('context_retrieval')
        retrieval_tasks = [context_manager.get_context(ctx.workflow_id) for ctx in contexts]
        retrieved_contexts = await asyncio.gather(*retrieval_tasks)
        retrieval_time = performance_monitor.end_timing('context_retrieval')
        
        # Measure context update performance
        performance_monitor.start_timing('context_updates')
        for i, context in enumerate(contexts):
            context.current_step = 'shortage_analysis'
            context.metadata['updated_at'] = time.time()
            context.metadata['update_count'] = i
            await context_manager.store_context(context)
        update_time = performance_monitor.end_timing('context_updates')
        
        total_time = performance_monitor.end_timing('context_operations')
        memory_usage = memory_usage_monitor.get_memory_usage()
        
        # Validate context performance
        context_count = len(contexts)
        
        # Storage performance
        storage_rate = context_count / storage_time
        assert storage_rate > 20, f"Context storage rate {storage_rate:.1f} contexts/s too slow"
        
        # Retrieval performance
        retrieval_rate = context_count / retrieval_time
        assert retrieval_rate > 50, f"Context retrieval rate {retrieval_rate:.1f} contexts/s too slow"
        
        # Update performance
        update_rate = context_count / update_time
        assert update_rate > 30, f"Context update rate {update_rate:.1f} contexts/s too slow"
        
        # Memory efficiency
        expected_memory_mb = (context_count * 1) / 1024  # 1KB per context
        actual_memory_mb = memory_usage['increase_mb']
        memory_efficiency = expected_memory_mb / actual_memory_mb if actual_memory_mb > 0 else 1
        
        assert memory_efficiency > 0.1, f"Memory efficiency too low: {memory_efficiency:.2f}"
        
        # Validate retrieved contexts
        assert len(retrieved_contexts) == context_count, "Should retrieve all stored contexts"
        for retrieved in retrieved_contexts:
            assert retrieved is not None, "All contexts should be retrievable"
        
        print(f"✓ Context performance: {context_count} contexts, "
              f"storage: {storage_rate:.1f}/s, retrieval: {retrieval_rate:.1f}/s, "
              f"updates: {update_rate:.1f}/s, memory: {actual_memory_mb:.1f}MB")