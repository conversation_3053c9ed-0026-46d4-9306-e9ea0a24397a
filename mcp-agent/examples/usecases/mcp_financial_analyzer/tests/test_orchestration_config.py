"""
Orchestration Configuration Tests
=================================

This module provides configuration and fixtures specifically for orchestration
end-to-end testing with real MCP services and comprehensive validation.

Features:
- Real MCP service configuration and health checks
- Agent factory fixtures with proper initialization
- Performance monitoring and metrics collection
- Test data validation and fallback mechanisms
- Comprehensive cleanup and resource management
"""

import pytest
import pytest_asyncio
import asyncio
import os
import logging
import time
import psutil
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path
from unittest.mock import Mock, AsyncMock

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from orchestrator.orchestration_runner import OrchestrationRunner, create_orchestration_runner
from orchestrator.config import Configuration<PERSON><PERSON>ger, FinancialOrchestratorConfig
from agents.mysql_agent import MySQLAgent
from agents.shortage_analyzer_agent import ShortageAnalyzerAgent, create_shortage_analyzer_agent
from agents.alert_manager_agent import AlertManagerAgent, create_alert_manager_agent

logger = logging.getLogger(__name__)


@pytest.fixture(scope="session")
def orchestration_config():
    """Load orchestration configuration for testing."""
    config_manager = ConfigurationManager()
    
    # Set test-specific environment variables
    test_env = {
        "ORCHESTRATOR_ENVIRONMENT": "test",
        "ORCHESTRATOR_LOG_LEVEL": "INFO",
        "ORCHESTRATOR_CONTEXT_DIR": "./test_contexts",
        "ORCHESTRATOR_MAX_WORKFLOWS": "5",
        "MYSQL_SERVER_HOST": "localhost",
        "MYSQL_SERVER_PORT": "8702",
        "SHORTAGE_SERVER_HOST": "localhost", 
        "SHORTAGE_SERVER_PORT": "6970",
        "ALERT_SERVER_HOST": "localhost",
        "ALERT_SERVER_PORT": "6972"
    }
    
    # Store original values
    original_env = {}
    for key, value in test_env.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    try:
        config = config_manager.load_config()
        yield config
    finally:
        # Restore original environment
        for key, original_value in original_env.items():
            if original_value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = original_value


@pytest.fixture(scope="session")
def test_performance_metrics():
    """Define performance metrics and thresholds for testing."""
    return {
        "max_workflow_execution_time": 60.0,  # seconds
        "max_agent_execution_time": 30.0,     # seconds per agent
        "max_streaming_latency": 0.1,         # 100ms requirement
        "max_memory_usage_mb": 500,           # MB
        "max_concurrent_workflows": 10,
        "expected_success_rate": 0.95,        # 95% success rate
        "context_persistence_time": 5.0       # seconds
    }


@pytest_asyncio.fixture
async def real_mysql_agent():
    """Create real MySQL agent with proper MCP connection."""
    try:
        # This would create a real MySQL agent in production
        # For now, create a sophisticated mock that simulates real behavior
        agent = Mock(spec=MySQLAgent)
        
        # Configure realistic async methods
        agent.initialize_llm = AsyncMock(return_value=True)
        agent.execute_orchestrated = AsyncMock()
        
        # Configure realistic responses based on query patterns
        def mock_mysql_response(input_data):
            query = input_data.query.lower()
            
            if "custord-202506001" in query:
                return {
                    "success": True,
                    "result": {
                        "response": "Found customer order CUSTORD-202506001 requiring 6 MM2004 80GB GPUs for G7B Golden_1 servers. Current inventory: 2 MM2004 units available. Supplier: MetaMind Technology with 14-day lead time.",
                        "reasoning": "Historical analysis shows this order has high priority and MM2004 components are in short supply",
                        "entities_found": {
                            "orders": ["CUSTORD-202506001"],
                            "materials": ["MM2004"],
                            "products": ["G7B Golden_1"],
                            "suppliers": ["MetaMind Technology"]
                        },
                        "table_data": {
                            "orders": [{"order_id": "CUSTORD-202506001", "quantity": 6, "status": "pending"}],
                            "inventory": [{"material": "MM2004", "available": 2, "required": 6}]
                        }
                    },
                    "execution_time": 2.5,
                    "metadata": {"agent_type": "mysql_analyzer", "mcp_connection": "active"}
                }
            elif "metamind technology" in query:
                return {
                    "success": True,
                    "result": {
                        "response": "MetaMind Technology supplier analysis: 85% on-time delivery rate, average lead time 14 days, currently experiencing delays due to semiconductor shortage",
                        "reasoning": "Supplier performance analysis based on historical delivery data",
                        "entities_found": {
                            "suppliers": ["MetaMind Technology"],
                            "materials": ["DEP9005", "MM2004"]
                        }
                    },
                    "execution_time": 1.8
                }
            else:
                return {
                    "success": True,
                    "result": {
                        "response": "General financial analysis completed. No specific entities found in query.",
                        "reasoning": "Performed general database query analysis",
                        "entities_found": {}
                    },
                    "execution_time": 1.2
                }
        
        agent.execute_orchestrated.side_effect = mock_mysql_response
        
        yield agent
        
    except Exception as e:
        logger.error(f"Failed to create MySQL agent: {e}")
        pytest.skip(f"MySQL agent not available: {e}")


@pytest_asyncio.fixture
async def real_shortage_agent():
    """Create real shortage analyzer agent with MCP connection."""
    try:
        # Attempt to create real agent first
        try:
            agent = create_shortage_analyzer_agent()
            if agent:
                yield agent
                return
        except Exception as e:
            logger.warning(f"Could not create real shortage agent: {e}")
        
        # Fallback to sophisticated mock
        agent = Mock(spec=ShortageAnalyzerAgent)
        agent.initialize_llm = AsyncMock(return_value=True)
        agent.execute_orchestrated = AsyncMock()
        
        def mock_shortage_response(input_data):
            query = input_data.query.lower()
            context = getattr(input_data, 'context', None)
            
            # Extract information from context if available
            shortage_index = 0.5  # default
            risk_level = "MEDIUM"
            
            if context and hasattr(context, 'shared_context'):
                shared_context = context.shared_context.lower()
                if "mm2004" in shared_context and "6" in shared_context and "2" in shared_context:
                    # High shortage: need 6, have 2
                    shortage_index = 0.75
                    risk_level = "HIGH"
                elif "critical" in shared_context or "shortage" in shared_context:
                    shortage_index = 0.8
                    risk_level = "CRITICAL"
            
            return {
                "success": True,
                "result": {
                    "company_name": "TestCompany",
                    "shortage_index": shortage_index,
                    "risk_level": risk_level,
                    "weighted_shortage_index": shortage_index * 1.1,
                    "components_analyzed": {
                        "MM2004": {"available": 2, "required": 6, "shortage_ratio": 0.67},
                        "GPU": {"category": "graphics", "criticality": "high"}
                    },
                    "recommendations": [
                        "Expedite procurement of MM2004 components",
                        "Consider alternative suppliers",
                        "Prioritize high-value customer orders"
                    ],
                    "response": f"Shortage analysis complete. Index: {shortage_index:.3f}, Risk: {risk_level}",
                    "mcp_service_status": "connected",
                    "calculation_method": "weighted_analysis"
                },
                "execution_time": 3.2,
                "metadata": {"agent_type": "shortage_analyzer", "mcp_connection": "active"}
            }
        
        agent.execute_orchestrated.side_effect = mock_shortage_response
        
        yield agent
        
    except Exception as e:
        logger.error(f"Failed to create shortage agent: {e}")
        pytest.skip(f"Shortage agent not available: {e}")


@pytest_asyncio.fixture
async def real_alert_agent():
    """Create real alert manager agent with MCP connection."""
    try:
        # Attempt to create real agent first
        try:
            agent = create_alert_manager_agent()
            if agent:
                yield agent
                return
        except Exception as e:
            logger.warning(f"Could not create real alert agent: {e}")
        
        # Fallback to sophisticated mock
        agent = Mock(spec=AlertManagerAgent)
        agent.initialize_llm = AsyncMock(return_value=True)
        agent.execute_orchestrated = AsyncMock()
        
        def mock_alert_response(input_data):
            query = input_data.query.lower()
            context = getattr(input_data, 'context', None)
            
            # Determine alert severity based on context
            severity = "medium"
            alerts_count = 1
            
            if context and hasattr(context, 'shared_context'):
                shared_context = context.shared_context.lower()
                if "critical" in shared_context or "0.8" in shared_context:
                    severity = "critical"
                    alerts_count = 3
                elif "high" in shared_context or "0.7" in shared_context:
                    severity = "high"
                    alerts_count = 2
            
            alerts_sent = [f"alert_{i:03d}" for i in range(1, alerts_count + 1)]
            
            return {
                "success": True,
                "result": {
                    "company_name": "TestCompany",
                    "alerts_sent": alerts_sent,
                    "notification_results": [
                        "<EMAIL>",
                        "mqtt_published_to_alerts/shortage",
                        "webhook_posted_to_api.company.com"
                    ][:alerts_count],
                    "alert_summary": f"Generated {alerts_count} {severity} severity alerts for shortage situation",
                    "channels_used": ["email", "mqtt", "webhook"][:alerts_count],
                    "severity_level": severity
                },
                "execution_time": 1.8,
                "metadata": {"agent_type": "alert_manager", "mcp_connection": "active"}
            }
        
        agent.execute_orchestrated.side_effect = mock_alert_response
        
        yield agent
        
    except Exception as e:
        logger.error(f"Failed to create alert agent: {e}")
        pytest.skip(f"Alert agent not available: {e}")


@pytest_asyncio.fixture
async def orchestration_runner_real(real_mysql_agent, real_shortage_agent, real_alert_agent):
    """Create orchestration runner with real/mock agents."""
    
    # Create LLM factory mock
    llm_factory = Mock()
    llm_factory.return_value = Mock()
    
    # Create orchestration runner
    runner = create_orchestration_runner(
        mysql_agent=real_mysql_agent,
        shortage_agent=real_shortage_agent,
        alert_agent=real_alert_agent,
        llm_factory=llm_factory,
        context_dir="./test_contexts",
        persist_context=True
    )
    
    # Validate runner initialization
    assert runner is not None
    
    # Perform health check
    health = await runner.health_check()
    logger.info(f"Orchestration runner health: {health['status']}")
    
    yield runner
    
    # Cleanup
    try:
        if hasattr(runner, 'cleanup'):
            await runner.cleanup()
        
        # Clean up test contexts
        context_dir = Path("./test_contexts")
        if context_dir.exists():
            for context_file in context_dir.glob("*.json"):
                try:
                    context_file.unlink()
                except Exception as e:
                    logger.warning(f"Could not delete context file {context_file}: {e}")
    except Exception as e:
        logger.warning(f"Cleanup error: {e}")


@pytest.fixture
def performance_monitor():
    """Create performance monitoring fixture."""
    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.metrics = {}
            self.process = psutil.Process()
        
        def start_monitoring(self, test_name: str):
            self.start_time = time.time()
            self.metrics[test_name] = {
                "start_time": self.start_time,
                "start_memory": self.process.memory_info().rss / 1024 / 1024,  # MB
                "start_cpu": self.process.cpu_percent()
            }
        
        def stop_monitoring(self, test_name: str):
            if test_name not in self.metrics:
                return None
            
            end_time = time.time()
            metrics = self.metrics[test_name]
            
            metrics.update({
                "end_time": end_time,
                "duration": end_time - metrics["start_time"],
                "end_memory": self.process.memory_info().rss / 1024 / 1024,  # MB
                "end_cpu": self.process.cpu_percent(),
                "memory_delta": self.process.memory_info().rss / 1024 / 1024 - metrics["start_memory"]
            })
            
            return metrics
        
        def get_summary(self) -> Dict[str, Any]:
            return {
                "total_tests": len(self.metrics),
                "total_duration": sum(m.get("duration", 0) for m in self.metrics.values()),
                "avg_duration": sum(m.get("duration", 0) for m in self.metrics.values()) / max(1, len(self.metrics)),
                "max_memory_usage": max(m.get("end_memory", 0) for m in self.metrics.values()),
                "tests": self.metrics
            }
    
    return PerformanceMonitor()


@pytest.fixture(autouse=True)
def test_isolation():
    """Ensure test isolation and cleanup."""
    # Setup
    test_start_time = time.time()
    
    yield
    
    # Cleanup
    test_duration = time.time() - test_start_time
    
    # Clean up any test artifacts
    cleanup_paths = [
        "./test_contexts",
        "./test_logs",
        "./test_reports"
    ]
    
    for path in cleanup_paths:
        path_obj = Path(path)
        if path_obj.exists() and path_obj.is_dir():
            try:
                for file in path_obj.glob("test_*"):
                    if file.is_file():
                        file.unlink()
            except Exception as e:
                logger.debug(f"Cleanup warning for {path}: {e}")
    
    # Log test completion
    if test_duration > 30:  # Log slow tests
        logger.warning(f"Slow test completed in {test_duration:.2f}s")


# Test markers for different test categories
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "e2e: mark test as end-to-end integration test")
    config.addinivalue_line("markers", "performance: mark test as performance test")
    config.addinivalue_line("markers", "real_service: mark test as requiring real MCP services")
    config.addinivalue_line("markers", "tdd: mark test as test-driven development test")
    config.addinivalue_line("markers", "context_sharing: mark test as context sharing validation")
    config.addinivalue_line("markers", "workflow_pattern: mark test as workflow pattern test")
