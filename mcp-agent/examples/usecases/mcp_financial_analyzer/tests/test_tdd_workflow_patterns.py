"""
TDD Workflow Pattern Tests for Financial Analyzer Orchestration
==============================================================

This module implements Test-Driven Development (TDD) tests for workflow patterns.
Tests are written first to define expected behavior, then orchestration logic
is implemented/fixed to make tests pass.

Test Categories:
1. Workflow Pattern Definition Tests (TDD)
2. Pattern Execution Logic Tests (TDD)
3. Error Handling and Recovery Tests (TDD)
4. Backward Compatibility Tests (TDD)

TDD Approach:
- Write failing tests first that define expected behavior
- Implement orchestration logic to make tests pass
- Refactor while maintaining test coverage
"""

import pytest
import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch, AsyncMock

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from orchestrator.workflow_patterns import (
    WorkflowPatternRegistry, WorkflowPattern, WorkflowStep, ExecutionMode, WorkflowExecutor
)
from orchestrator.query_processor import FinancialQueryProcessor, QueryType
from orchestrator.context_manager import ContextManager


class TestTDDWorkflowPatternDefinition:
    """TDD tests for workflow pattern definitions."""
    
    @pytest.fixture
    def pattern_registry(self):
        """Create workflow pattern registry for testing."""
        return WorkflowPatternRegistry()
    
    def test_shortage_analysis_pattern_definition(self, pattern_registry):
        """
        TDD Test: Shortage analysis pattern should be properly defined.
        
        This test defines the expected structure and behavior of the
        shortage analysis workflow pattern. Initially fails until
        pattern is properly implemented.
        """
        # This test defines what we expect from the shortage analysis pattern
        pattern = pattern_registry.get_pattern("shortage_analysis")
        
        # Pattern should exist
        assert pattern is not None, "Shortage analysis pattern not found"
        
        # Pattern should have correct structure
        assert pattern.pattern_id == "shortage_analysis"
        assert pattern.name == "Shortage Analysis Workflow"
        assert len(pattern.steps) == 3  # MySQL → Shortage → Alert
        
        # Validate step sequence
        step_names = [step.agent_name for step in pattern.steps]
        expected_sequence = ["mysql_analyzer", "shortage_analyzer", "alert_manager"]
        assert step_names == expected_sequence, f"Expected {expected_sequence}, got {step_names}"
        
        # Validate step dependencies
        mysql_step = pattern.get_step("mysql_data_analysis")
        shortage_step = pattern.get_step("shortage_calculation")
        alert_step = pattern.get_step("alert_processing")
        
        assert mysql_step is not None
        assert shortage_step is not None
        assert alert_step is not None
        
        # MySQL step should have no dependencies
        assert len(mysql_step.dependencies) == 0
        
        # Shortage step should depend on MySQL
        assert "mysql_data_analysis" in shortage_step.dependencies
        
        # Alert step should depend on shortage
        assert "shortage_calculation" in alert_step.dependencies
        
        # Validate execution mode
        assert pattern.execution_mode == ExecutionMode.SEQUENTIAL
    
    def test_supplier_risk_pattern_definition(self, pattern_registry):
        """
        TDD Test: Supplier risk pattern should be properly defined.
        
        This test defines the expected structure for supplier risk analysis
        which should only involve MySQL and shortage analysis (no alerts).
        """
        pattern = pattern_registry.get_pattern("supplier_risk")
        
        # Pattern should exist
        assert pattern is not None, "Supplier risk pattern not found"
        
        # Should have only 2 steps (MySQL → Shortage)
        assert len(pattern.steps) == 2
        
        step_names = [step.agent_name for step in pattern.steps]
        expected_sequence = ["mysql_analyzer", "shortage_analyzer"]
        assert step_names == expected_sequence
        
        # Alert step should not be present
        alert_step = pattern.get_step("alert_processing")
        assert alert_step is None, "Supplier risk pattern should not include alerts"
    
    def test_comprehensive_pattern_definition(self, pattern_registry):
        """
        TDD Test: Comprehensive pattern should include all agents with parallel options.
        
        This test defines the most complex workflow pattern that uses all
        available agents with potential parallel execution.
        """
        pattern = pattern_registry.get_pattern("comprehensive")
        
        assert pattern is not None, "Comprehensive pattern not found"
        assert len(pattern.steps) >= 3  # At least MySQL → Shortage → Alert
        
        # Should support hybrid execution mode for optimization
        assert pattern.execution_mode in [ExecutionMode.SEQUENTIAL, ExecutionMode.HYBRID]
        
        # Should have longer estimated duration
        assert pattern.estimated_duration >= 300  # At least 5 minutes
        
        # Should have comprehensive success criteria
        assert len(pattern.success_criteria) >= 3
        expected_criteria = ["mysql_analysis_complete", "shortage_calculation_complete", "alert_delivery_complete"]
        for criterion in expected_criteria:
            assert criterion in pattern.success_criteria
    
    def test_pattern_execution_order_calculation(self, pattern_registry):
        """
        TDD Test: Pattern execution order should be calculated correctly.
        
        This test validates that workflow patterns correctly calculate
        execution order based on dependencies and parallel execution options.
        """
        pattern = pattern_registry.get_pattern("shortage_analysis")
        assert pattern is not None
        
        # Get execution order
        execution_order = pattern.get_execution_order()
        
        # Should return list of execution groups
        assert isinstance(execution_order, list)
        assert len(execution_order) > 0
        
        # For sequential pattern, each group should have one step
        for group in execution_order:
            assert isinstance(group, list)
            if pattern.execution_mode == ExecutionMode.SEQUENTIAL:
                assert len(group) == 1
        
        # Validate dependency order
        all_steps = [step for group in execution_order for step in group]
        
        # MySQL should come before shortage
        mysql_index = next(i for i, step in enumerate(all_steps) if "mysql" in step)
        shortage_index = next(i for i, step in enumerate(all_steps) if "shortage" in step)
        assert mysql_index < shortage_index, "MySQL should execute before shortage analysis"
        
        # Shortage should come before alert
        if len(all_steps) > 2:
            alert_index = next(i for i, step in enumerate(all_steps) if "alert" in step)
            assert shortage_index < alert_index, "Shortage should execute before alert"


class TestTDDPatternExecutionLogic:
    """TDD tests for pattern execution logic."""
    
    @pytest.fixture
    def workflow_executor(self):
        """Create workflow executor for testing."""
        registry = WorkflowPatternRegistry()
        return WorkflowExecutor(registry)
    
    @pytest.fixture
    def mock_agents(self):
        """Create mock agents for testing."""
        mysql_agent = Mock()
        mysql_agent.execute_orchestrated = AsyncMock(return_value={
            "success": True,
            "result": {"response": "MySQL analysis complete", "entities_found": ["CUSTORD-202506001"]},
            "execution_time": 2.5
        })
        
        shortage_agent = Mock()
        shortage_agent.execute_orchestrated = AsyncMock(return_value={
            "success": True,
            "result": {"shortage_index": 0.75, "risk_level": "HIGH"},
            "execution_time": 3.2
        })
        
        alert_agent = Mock()
        alert_agent.execute_orchestrated = AsyncMock(return_value={
            "success": True,
            "result": {"alerts_sent": ["alert_001"], "notification_results": ["email_sent"]},
            "execution_time": 1.8
        })
        
        return {
            "mysql_analyzer": mysql_agent,
            "shortage_analyzer": shortage_agent,
            "alert_manager": alert_agent
        }
    
    @pytest.fixture
    def context_manager(self):
        """Create context manager for testing."""
        return ContextManager(persist_context=False)
    
    @pytest.mark.asyncio
    async def test_sequential_pattern_execution(
        self,
        workflow_executor,
        mock_agents,
        context_manager
    ):
        """
        TDD Test: Sequential pattern execution should work correctly.
        
        This test defines the expected behavior for sequential workflow
        execution where agents run one after another.
        """
        workflow_id = f"test_sequential_{uuid.uuid4().hex[:8]}"
        
        # Create context
        context = context_manager.create_context(
            workflow_id=workflow_id,
            query="Test sequential execution",
            query_type="shortage_analysis",
            workflow_pattern="shortage_analysis"
        )
        
        # Execute pattern
        results = await workflow_executor.execute_pattern(
            pattern_id="shortage_analysis",
            workflow_id=workflow_id,
            agents=mock_agents,
            context_manager=context_manager,
            input_data={"original_query": "Test query"}
        )
        
        # Validate execution results
        assert results["success"] is True
        assert "step_results" in results
        
        step_results = results["step_results"]
        
        # All steps should have executed
        assert "mysql_data_analysis" in step_results
        assert "shortage_calculation" in step_results
        assert "alert_processing" in step_results
        
        # All steps should have succeeded
        for step_id, result in step_results.items():
            assert result["success"] is True
            assert result["execution_time"] > 0
        
        # Validate execution order (MySQL → Shortage → Alert)
        mysql_result = step_results["mysql_data_analysis"]
        shortage_result = step_results["shortage_calculation"]
        alert_result = step_results["alert_processing"]
        
        # Each agent should have been called once
        mock_agents["mysql_analyzer"].execute_orchestrated.assert_called_once()
        mock_agents["shortage_analyzer"].execute_orchestrated.assert_called_once()
        mock_agents["alert_manager"].execute_orchestrated.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_pattern_execution_with_context_sharing(
        self,
        workflow_executor,
        mock_agents,
        context_manager
    ):
        """
        TDD Test: Pattern execution should share context between agents.
        
        This test validates that context data flows correctly from one
        agent to the next during pattern execution.
        """
        workflow_id = f"test_context_sharing_{uuid.uuid4().hex[:8]}"
        
        # Execute pattern
        results = await workflow_executor.execute_pattern(
            pattern_id="shortage_analysis",
            workflow_id=workflow_id,
            agents=mock_agents,
            context_manager=context_manager,
            input_data={"original_query": "Test context sharing"}
        )
        
        assert results["success"] is True
        
        # Validate that context was updated after each step
        final_context = context_manager.get_full_context_summary(workflow_id)
        
        # Should have context from all agents
        assert "mysql_context" in final_context
        assert "shortage_context" in final_context
        assert "alert_context" in final_context
        
        # Context should contain results from each agent
        mysql_ctx = final_context["mysql_context"]
        shortage_ctx = final_context["shortage_context"]
        alert_ctx = final_context["alert_context"]
        
        assert mysql_ctx["success"] is True
        assert shortage_ctx["success"] is True
        assert alert_ctx["success"] is True
        
        # Shortage context should reference MySQL results
        assert shortage_ctx["shortage_index"] == 0.75
        
        # Alert context should reference shortage results
        assert len(alert_ctx["alerts_sent"]) > 0
