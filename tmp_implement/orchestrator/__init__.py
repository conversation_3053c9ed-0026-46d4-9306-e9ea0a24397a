"""
MCP Financial Analyzer Orchestrator Package

This package provides orchestration capabilities for the MCP Financial Analyzer system,
coordinating three specialized agents:
- MySQL Agent: Database queries and data retrieval
- Shortage Analyzer Agent: Storage capacity analysis and recommendations
- Alert Manager Agent: Alert generation and notifications

Key Components:
- FinancialOrchestrator: Main orchestrator class
- WorkflowContext: Shared context management system
- QueryParser: Query analysis and workflow planning
- AgentType, WorkflowType: Enums for type safety

Usage:
    from orchestrator import create_financial_orchestrator
    
    orchestrator = create_financial_orchestrator("YourCompany")
    await orchestrator.initialize_agents()
    
    result = await orchestrator.process_query("Analyze GPU shortage for customer orders")
    print(result["response"])
"""

from .financial_orchestrator import FinancialOrchestrator, create_financial_orchestrator
from .context_manager import (
    WorkflowContext, AgentType, AgentTrajectory, ToolCall, ExecutionStatus,
    create_new_context, get_current_context, set_current_context
)
from .query_parser import QueryParser, WorkflowType, WorkflowPlan, AgentExecutionPlan

__all__ = [
    # Main orchestrator
    'FinancialOrchestrator',
    'create_financial_orchestrator',
    
    # Context management
    'WorkflowContext',
    'AgentType',
    'AgentTrajectory', 
    'ToolCall',
    'ExecutionStatus',
    'create_new_context',
    'get_current_context',
    'set_current_context',
    
    # Query parsing
    'QueryParser',
    'WorkflowType',
    'WorkflowPlan',
    'AgentExecutionPlan'
]

__version__ = "1.0.0"
__author__ = "MCP Financial Analyzer Team"
__description__ = "Orchestrator for coordinating specialized financial analysis agents"
