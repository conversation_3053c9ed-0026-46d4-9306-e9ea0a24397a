"""
Context Management System for MCP Financial Analyzer Orchestrator

This module provides a shared context system that logs and stores:
- Each agent's complete trajectory (input → processing → output)
- All tool calls made by each agent with parameters and results
- LLM outputs and reasoning steps from each agent
- Decision-making context for agent sequencing

The context is accessible to all agents for informed decision-making and enables
agents to reason about previous steps and determine which agent should be called next.
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class AgentType(Enum):
    """Types of agents in the MCP financial analyzer system."""
    MYSQL = "mysql"
    SHORTAGE_ANALYZER = "shortage_analyzer"
    ALERT_MANAGER = "alert_manager"
    ORCHESTRATOR = "orchestrator"


class ExecutionStatus(Enum):
    """Status of agent execution."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class ToolCall:
    """Represents a tool call made by an agent."""
    tool_name: str
    parameters: Dict[str, Any]
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: datetime = None
    execution_time_ms: Optional[float] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class AgentTrajectory:
    """Complete trajectory of an agent's execution."""
    agent_id: str
    agent_type: AgentType
    input_data: Dict[str, Any]
    output_data: Optional[Dict[str, Any]] = None
    reasoning: Optional[str] = None
    tool_calls: List[ToolCall] = None
    llm_outputs: List[str] = None
    status: ExecutionStatus = ExecutionStatus.PENDING
    start_time: datetime = None
    end_time: Optional[datetime] = None
    error: Optional[str] = None

    def __post_init__(self):
        if self.tool_calls is None:
            self.tool_calls = []
        if self.llm_outputs is None:
            self.llm_outputs = []
        if self.start_time is None:
            self.start_time = datetime.now()

    @property
    def execution_time_ms(self) -> Optional[float]:
        """Calculate execution time in milliseconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds() * 1000
        return None

    def add_tool_call(self, tool_call: ToolCall):
        """Add a tool call to the trajectory."""
        self.tool_calls.append(tool_call)

    def add_llm_output(self, output: str):
        """Add an LLM output to the trajectory."""
        self.llm_outputs.append(output)

    def complete(self, output_data: Dict[str, Any], reasoning: Optional[str] = None):
        """Mark the trajectory as completed."""
        self.output_data = output_data
        self.reasoning = reasoning
        self.status = ExecutionStatus.COMPLETED
        self.end_time = datetime.now()

    def fail(self, error: str):
        """Mark the trajectory as failed."""
        self.error = error
        self.status = ExecutionStatus.FAILED
        self.end_time = datetime.now()


class WorkflowContext:
    """
    Shared context system for the MCP financial analyzer workflow.
    
    Manages agent trajectories, decision-making context, and inter-agent communication.
    """

    def __init__(self, workflow_id: Optional[str] = None):
        self.workflow_id = workflow_id or str(uuid.uuid4())
        self.trajectories: Dict[str, AgentTrajectory] = {}
        self.execution_order: List[str] = []
        self.workflow_metadata: Dict[str, Any] = {
            "created_at": datetime.now(),
            "user_query": None,
            "workflow_type": None,
            "expected_agents": []
        }
        self.shared_data: Dict[str, Any] = {}
        self.decision_log: List[Dict[str, Any]] = []

    def set_user_query(self, query: str, workflow_type: Optional[str] = None):
        """Set the initial user query that triggered this workflow."""
        self.workflow_metadata["user_query"] = query
        self.workflow_metadata["workflow_type"] = workflow_type

    def create_agent_trajectory(self, agent_type: AgentType, input_data: Dict[str, Any]) -> str:
        """Create a new agent trajectory and return its ID."""
        agent_id = f"{agent_type.value}_{str(uuid.uuid4())[:8]}"
        trajectory = AgentTrajectory(
            agent_id=agent_id,
            agent_type=agent_type,
            input_data=input_data
        )
        self.trajectories[agent_id] = trajectory
        self.execution_order.append(agent_id)
        return agent_id

    def get_trajectory(self, agent_id: str) -> Optional[AgentTrajectory]:
        """Get a specific agent trajectory."""
        return self.trajectories.get(agent_id)

    def get_trajectories_by_type(self, agent_type: AgentType) -> List[AgentTrajectory]:
        """Get all trajectories for a specific agent type."""
        return [t for t in self.trajectories.values() if t.agent_type == agent_type]

    def get_completed_trajectories(self) -> List[AgentTrajectory]:
        """Get all completed trajectories in execution order."""
        return [
            self.trajectories[agent_id] 
            for agent_id in self.execution_order 
            if self.trajectories[agent_id].status == ExecutionStatus.COMPLETED
        ]

    def get_last_completed_trajectory(self) -> Optional[AgentTrajectory]:
        """Get the most recently completed trajectory."""
        completed = self.get_completed_trajectories()
        return completed[-1] if completed else None

    def add_shared_data(self, key: str, value: Any):
        """Add data to the shared context."""
        self.shared_data[key] = value

    def get_shared_data(self, key: str, default: Any = None) -> Any:
        """Get data from the shared context."""
        return self.shared_data.get(key, default)

    def log_decision(self, decision_type: str, reasoning: str, context: Dict[str, Any]):
        """Log a decision made by the orchestrator."""
        self.decision_log.append({
            "timestamp": datetime.now(),
            "decision_type": decision_type,
            "reasoning": reasoning,
            "context": context
        })

    def get_context_summary(self) -> Dict[str, Any]:
        """Get a summary of the current workflow context."""
        return {
            "workflow_id": self.workflow_id,
            "user_query": self.workflow_metadata["user_query"],
            "workflow_type": self.workflow_metadata["workflow_type"],
            "total_agents": len(self.trajectories),
            "completed_agents": len(self.get_completed_trajectories()),
            "execution_order": self.execution_order,
            "agent_statuses": {
                agent_id: trajectory.status.value 
                for agent_id, trajectory in self.trajectories.items()
            },
            "shared_data_keys": list(self.shared_data.keys()),
            "decision_count": len(self.decision_log)
        }

    def get_context_for_agent(self, requesting_agent_type: AgentType) -> Dict[str, Any]:
        """
        Get relevant context for a specific agent type.
        
        This method provides filtered context that helps agents make informed decisions
        about their processing and determine what other agents might need to be called.
        """
        context = {
            "workflow_summary": self.get_context_summary(),
            "user_query": self.workflow_metadata["user_query"],
            "previous_results": {},
            "shared_data": self.shared_data.copy(),
            "execution_history": []
        }

        # Add results from completed agents
        for trajectory in self.get_completed_trajectories():
            if trajectory.output_data:
                context["previous_results"][trajectory.agent_type.value] = {
                    "output": trajectory.output_data,
                    "reasoning": trajectory.reasoning,
                    "execution_time_ms": trajectory.execution_time_ms
                }

        # Add execution history
        for agent_id in self.execution_order:
            trajectory = self.trajectories[agent_id]
            context["execution_history"].append({
                "agent_type": trajectory.agent_type.value,
                "status": trajectory.status.value,
                "has_output": trajectory.output_data is not None
            })

        return context

    def to_dict(self) -> Dict[str, Any]:
        """Convert the entire context to a dictionary for serialization."""
        return {
            "workflow_id": self.workflow_id,
            "workflow_metadata": self.workflow_metadata,
            "trajectories": {
                agent_id: {
                    **asdict(trajectory),
                    "agent_type": trajectory.agent_type.value,
                    "status": trajectory.status.value
                }
                for agent_id, trajectory in self.trajectories.items()
            },
            "execution_order": self.execution_order,
            "shared_data": self.shared_data,
            "decision_log": self.decision_log
        }

    def save_to_file(self, filepath: str):
        """Save the context to a JSON file."""
        try:
            with open(filepath, 'w') as f:
                json.dump(self.to_dict(), f, indent=2, default=str)
            logger.info(f"Context saved to {filepath}")
        except Exception as e:
            logger.error(f"Failed to save context to {filepath}: {e}")

    @classmethod
    def load_from_file(cls, filepath: str) -> 'WorkflowContext':
        """Load context from a JSON file."""
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            context = cls(workflow_id=data["workflow_id"])
            context.workflow_metadata = data["workflow_metadata"]
            context.execution_order = data["execution_order"]
            context.shared_data = data["shared_data"]
            context.decision_log = data["decision_log"]
            
            # Reconstruct trajectories
            for agent_id, traj_data in data["trajectories"].items():
                trajectory = AgentTrajectory(
                    agent_id=traj_data["agent_id"],
                    agent_type=AgentType(traj_data["agent_type"]),
                    input_data=traj_data["input_data"],
                    output_data=traj_data.get("output_data"),
                    reasoning=traj_data.get("reasoning"),
                    status=ExecutionStatus(traj_data["status"]),
                    error=traj_data.get("error")
                )
                
                # Reconstruct tool calls
                if traj_data.get("tool_calls"):
                    trajectory.tool_calls = [
                        ToolCall(**call_data) for call_data in traj_data["tool_calls"]
                    ]
                
                if traj_data.get("llm_outputs"):
                    trajectory.llm_outputs = traj_data["llm_outputs"]
                
                context.trajectories[agent_id] = trajectory
            
            logger.info(f"Context loaded from {filepath}")
            return context
            
        except Exception as e:
            logger.error(f"Failed to load context from {filepath}: {e}")
            raise


# Global context manager for the current workflow
_current_context: Optional[WorkflowContext] = None


def get_current_context() -> Optional[WorkflowContext]:
    """Get the current workflow context."""
    return _current_context


def set_current_context(context: WorkflowContext):
    """Set the current workflow context."""
    global _current_context
    _current_context = context


def create_new_context(user_query: str, workflow_type: Optional[str] = None) -> WorkflowContext:
    """Create a new workflow context and set it as current."""
    context = WorkflowContext()
    context.set_user_query(user_query, workflow_type)
    set_current_context(context)
    return context
