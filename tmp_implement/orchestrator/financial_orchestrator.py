"""
Financial Orchestrator Agent for MCP Financial Analyzer System

This orchestrator coordinates three specialized agents:
- MySQL Agent: Database queries and data retrieval
- Storage Analyzer Agent: Storage capacity analysis and recommendations  
- Alert Manager Agent: Alert generation and notifications

The orchestrator parses user queries, routes them to appropriate agents in the correct sequence,
handles responses, and provides consolidated responses back to the user.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from .context_manager import (
    WorkflowContext, AgentType, AgentTrajectory, ToolCall, ExecutionStatus,
    create_new_context, get_current_context
)
from .query_parser import QueryParser, WorkflowPlan, AgentExecutionPlan

# Import existing agent creation functions
from agents.mysql_agent import (
    create_mysql_orchestrator_agent, MCPOrchestratorInputSchema, 
    safe_orchestrator_run, FinalResponseSchema, tool_schema_to_class_map
)
from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from agents.alert_manager_agent import create_alert_manager_agent
from schemas.agent_schemas import (
    ShortageAnalysisInputSchema, AlertManagementInputSchema, 
    InstructionInputSchema, InstructionOutputSchema
)

logger = logging.getLogger(__name__)


class FinancialOrchestrator:
    """
    Main orchestrator for the MCP Financial Analyzer system.
    
    Coordinates MySQL, Shortage Analyzer, and Alert Manager agents based on user queries.
    Maintains shared context and provides consolidated responses.
    """

    def __init__(self, company_name: str = "DefaultCompany"):
        self.company_name = company_name
        self.query_parser = QueryParser()
        
        # Initialize agents (lazy loading)
        self._mysql_agent = None
        self._shortage_agent = None
        self._alert_agent = None
        
        # Current workflow context
        self._current_context: Optional[WorkflowContext] = None

    async def initialize_agents(self):
        """Initialize all three specialized agents."""
        logger.info("Initializing orchestrator agents...")
        
        try:
            # Create agents
            self._mysql_agent = create_mysql_orchestrator_agent()
            self._shortage_agent = create_shortage_analyzer_agent(self.company_name)
            self._alert_agent = create_alert_manager_agent(self.company_name)
            
            # Initialize LLMs if supported
            agents = [
                (self._mysql_agent, "MySQL"),
                (self._shortage_agent, "Shortage Analyzer"), 
                (self._alert_agent, "Alert Manager")
            ]
            
            for agent, name in agents:
                if hasattr(agent, 'initialize_llm'):
                    await agent.initialize_llm()
                    logger.info(f"✓ {name} agent LLM initialized")
                else:
                    logger.info(f"✓ {name} agent ready (no LLM initialization needed)")
            
            logger.info("✓ All orchestrator agents initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize agents: {e}")
            raise

    async def process_query(self, user_query: str) -> Dict[str, Any]:
        """
        Process a user query through the orchestrated workflow.
        
        Args:
            user_query: The user's input query
            
        Returns:
            Dict containing the consolidated response and execution details
        """
        logger.info(f"Processing query: {user_query}")
        
        # Ensure agents are initialized
        if not self._mysql_agent:
            await self.initialize_agents()
        
        # Parse query to determine workflow
        workflow_plan = self.query_parser.parse_query(user_query)
        logger.info(f"Workflow plan: {workflow_plan.workflow_type.value} with {len(workflow_plan.agent_plans)} agents")
        
        # Create new workflow context
        self._current_context = create_new_context(user_query, workflow_plan.workflow_type.value)
        
        try:
            # Execute workflow
            execution_results = await self._execute_workflow(workflow_plan)
            
            # Generate consolidated response
            consolidated_response = self._generate_consolidated_response(execution_results)
            
            # Save context for debugging/analysis
            context_summary = self._current_context.get_context_summary()
            
            return {
                "success": True,
                "response": consolidated_response,
                "workflow_type": workflow_plan.workflow_type.value,
                "execution_results": execution_results,
                "context_summary": context_summary,
                "confidence_score": workflow_plan.confidence_score
            }
            
        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "workflow_type": workflow_plan.workflow_type.value,
                "context_summary": self._current_context.get_context_summary() if self._current_context else None
            }

    async def _execute_workflow(self, workflow_plan: WorkflowPlan) -> Dict[str, Any]:
        """Execute the workflow plan by running agents in the correct sequence."""
        execution_results = {}
        execution_order = workflow_plan.get_execution_order()
        
        logger.info(f"Executing workflow with {len(execution_order)} agents")
        
        for i, agent_plan in enumerate(execution_order):
            logger.info(f"Executing agent {i+1}/{len(execution_order)}: {agent_plan.agent_type.value}")
            
            try:
                # Execute the agent
                result = await self._execute_agent(agent_plan)
                execution_results[agent_plan.agent_type.value] = result
                
                # Log successful execution
                logger.info(f"✓ {agent_plan.agent_type.value} completed successfully")
                
            except Exception as e:
                logger.error(f"✗ {agent_plan.agent_type.value} failed: {e}")
                
                # Mark as failed in context
                if self._current_context:
                    trajectory = self._current_context.get_trajectories_by_type(agent_plan.agent_type)
                    if trajectory:
                        trajectory[-1].fail(str(e))
                
                # Decide whether to continue or abort
                if not agent_plan.optional:
                    logger.error(f"Required agent {agent_plan.agent_type.value} failed, aborting workflow")
                    raise
                else:
                    logger.warning(f"Optional agent {agent_plan.agent_type.value} failed, continuing workflow")
                    execution_results[agent_plan.agent_type.value] = {"success": False, "error": str(e)}
        
        return execution_results

    async def _execute_agent(self, agent_plan: AgentExecutionPlan) -> Dict[str, Any]:
        """Execute a specific agent based on the execution plan."""
        agent_type = agent_plan.agent_type
        
        # Create trajectory in context
        if self._current_context:
            agent_id = self._current_context.create_agent_trajectory(agent_type, agent_plan.parameters)
            trajectory = self._current_context.get_trajectory(agent_id)
            trajectory.status = ExecutionStatus.IN_PROGRESS
        
        try:
            if agent_type == AgentType.MYSQL:
                result = await self._execute_mysql_agent(agent_plan)
            elif agent_type == AgentType.SHORTAGE_ANALYZER:
                result = await self._execute_shortage_agent(agent_plan)
            elif agent_type == AgentType.ALERT_MANAGER:
                result = await self._execute_alert_agent(agent_plan)
            else:
                raise ValueError(f"Unknown agent type: {agent_type}")
            
            # Mark as completed in context
            if self._current_context and trajectory:
                trajectory.complete(result)
            
            return result
            
        except Exception as e:
            # Mark as failed in context
            if self._current_context and trajectory:
                trajectory.fail(str(e))
            raise

    async def _execute_mysql_agent(self, agent_plan: AgentExecutionPlan) -> Dict[str, Any]:
        """Execute the MySQL agent."""
        query = agent_plan.parameters.get("query", "")
        
        # Get context from previous agents
        context = ""
        if self._current_context:
            context_data = self._current_context.get_context_for_agent(AgentType.MYSQL)
            context = f"Workflow context: {context_data.get('user_query', '')}"
        
        # Create input
        mysql_input = MCPOrchestratorInputSchema(query=query)
        
        # Execute with timeout
        async def mysql_execution():
            mysql_output = safe_orchestrator_run(self._mysql_agent, mysql_input)
            action_instance = mysql_output.action
            reasoning = mysql_output.reasoning
            
            # Continue execution until final response
            max_iterations = 10
            iteration = 0
            
            while not isinstance(action_instance, FinalResponseSchema) and iteration < max_iterations:
                schema_type = type(action_instance)
                ToolClass = tool_schema_to_class_map.get(schema_type)
                
                if not ToolClass:
                    logger.error(f"Unknown schema type: {schema_type.__name__}")
                    break
                
                tool_name = getattr(ToolClass, 'mcp_tool_name', 'unknown_tool')
                
                # Log tool call in context
                if self._current_context:
                    tool_call = ToolCall(tool_name=tool_name, parameters=action_instance.dict())
                    trajectory = self._current_context.get_trajectories_by_type(AgentType.MYSQL)[-1]
                    trajectory.add_tool_call(tool_call)
                
                tool_instance = ToolClass()
                tool_output = tool_instance.run(action_instance)
                
                # Update tool call result
                if self._current_context:
                    tool_call.result = {"result": tool_output.result}
                
                # Continue with next iteration
                result_message = MCPOrchestratorInputSchema(
                    query=f"Tool {tool_name} executed with result: {tool_output.result}"
                )
                self._mysql_agent.memory.add_message("system", result_message)
                
                mysql_output = safe_orchestrator_run(self._mysql_agent)
                action_instance = mysql_output.action
                reasoning = mysql_output.reasoning
                iteration += 1
            
            if isinstance(action_instance, FinalResponseSchema):
                response = action_instance.response_text
                return {"success": True, "response": response, "context": response}
            else:
                return {"success": False, "response": "Analysis incomplete", "context": reasoning}
        
        # Run with timeout
        result = await asyncio.wait_for(mysql_execution(), timeout=120.0)
        return result

    async def _execute_shortage_agent(self, agent_plan: AgentExecutionPlan) -> Dict[str, Any]:
        """Execute the Shortage Analyzer agent."""
        message = agent_plan.parameters.get("message", "")
        
        # Get context from previous agents (especially MySQL results)
        context = ""
        financial_data = ""
        
        if self._current_context:
            context_data = self._current_context.get_context_for_agent(AgentType.SHORTAGE_ANALYZER)
            mysql_results = context_data.get("previous_results", {}).get("mysql")
            
            if mysql_results:
                financial_data = mysql_results.get("output", {}).get("response", "")
                context = mysql_results.get("output", {}).get("context", "")
        
        # Create input
        shortage_input = {
            "company_name": self.company_name,
            "financial_data": financial_data,
            "message": message
        }
        
        # Execute with timeout
        async def shortage_execution():
            # Create proper input schema
            shortage_schema_input = ShortageAnalysisInputSchema(
                company_name=shortage_input["company_name"],
                financial_data=shortage_input["financial_data"],
                message=shortage_input["message"]
            )

            result = await self._shortage_agent.enhanced_shortage_analysis(shortage_schema_input)

            return {
                "success": True,
                "shortage_index": result.shortage_index,
                "risk_level": result.risk_level,
                "response": result.response,
                "company_name": result.company_name
            }
        
        result = await asyncio.wait_for(shortage_execution(), timeout=60.0)
        return result

    async def _execute_alert_agent(self, agent_plan: AgentExecutionPlan) -> Dict[str, Any]:
        """Execute the Alert Manager agent."""
        message = agent_plan.parameters.get("message", "")
        
        # Get context from previous agents
        shortage_result = None
        alert_message = message
        
        if self._current_context:
            context_data = self._current_context.get_context_for_agent(AgentType.ALERT_MANAGER)
            shortage_results = context_data.get("previous_results", {}).get("shortage_analyzer")
            
            if shortage_results:
                shortage_result = shortage_results.get("output", {})
                shortage_index = shortage_result.get("shortage_index", 0)
                risk_level = shortage_result.get("risk_level", "unknown")
                alert_message = f"Shortage analysis complete: Index {shortage_index:.3f}, Risk {risk_level}. {message}"
        
        if not shortage_result or not shortage_result.get("success"):
            return {"success": False, "error": "No shortage analysis results available"}
        
        # Create input
        shortage_data = (
            f"shortage_index is {shortage_result['shortage_index']:.3f}, "
            f"risk_level is {shortage_result['risk_level']}, "
            f"scenario: {self._current_context.workflow_metadata.get('workflow_type', 'unknown')}"
        )
        
        alert_input = AlertManagementInputSchema(
            company_name=shortage_result["company_name"],
            analysis_data=shortage_result["response"],
            shortage_data=shortage_data,
            alert_message=alert_message,
            message=f"Process shortage analysis and send notifications"
        )
        
        # Execute with timeout
        async def alert_execution():
            alert_result = await self._alert_agent.process_financial_analysis(alert_input)
            
            return {
                "success": True,
                "alerts_sent": alert_result.alerts_sent,
                "notification_results": alert_result.notification_results,
                "alert_summary": alert_result.alert_summary
            }
        
        result = await asyncio.wait_for(alert_execution(), timeout=30.0)
        return result

    def _generate_consolidated_response(self, execution_results: Dict[str, Any]) -> str:
        """Generate a consolidated response from all agent results."""
        response_parts = []
        
        # Add header
        response_parts.append("=== MCP Financial Analyzer - Orchestrated Analysis ===\n")
        
        # Add MySQL analysis results
        mysql_results = execution_results.get("mysql", {})
        if mysql_results.get("success"):
            response_parts.append("📊 DATABASE ANALYSIS:")
            response_parts.append(mysql_results.get("response", "No response available"))
            response_parts.append("")
        
        # Add shortage analysis results
        shortage_results = execution_results.get("shortage_analyzer", {})
        if shortage_results.get("success"):
            response_parts.append("⚠️ SHORTAGE RISK ASSESSMENT:")
            response_parts.append(f"Shortage Index: {shortage_results.get('shortage_index', 'N/A')}")
            response_parts.append(f"Risk Level: {shortage_results.get('risk_level', 'N/A')}")
            response_parts.append(shortage_results.get("response", "No analysis available"))
            response_parts.append("")
        
        # Add alert management results
        alert_results = execution_results.get("alert_manager", {})
        if alert_results.get("success"):
            response_parts.append("🚨 ALERT MANAGEMENT:")
            alerts_sent = alert_results.get("alerts_sent", [])
            response_parts.append(f"Alerts Generated: {len(alerts_sent)}")
            response_parts.append(alert_results.get("alert_summary", "No alert summary available"))
            response_parts.append("")
        
        # Add workflow summary
        if self._current_context:
            summary = self._current_context.get_context_summary()
            response_parts.append("📋 WORKFLOW SUMMARY:")
            response_parts.append(f"Workflow Type: {summary.get('workflow_type', 'Unknown')}")
            response_parts.append(f"Agents Executed: {summary.get('completed_agents', 0)}/{summary.get('total_agents', 0)}")
            response_parts.append("")
        
        return "\n".join(response_parts)


# Factory function for creating orchestrator instances
def create_financial_orchestrator(company_name: str = "DefaultCompany") -> FinancialOrchestrator:
    """
    Factory function to create FinancialOrchestrator instances.
    
    Args:
        company_name: Name of the company for analysis
        
    Returns:
        FinancialOrchestrator instance ready for use
    """
    return FinancialOrchestrator(company_name=company_name)
