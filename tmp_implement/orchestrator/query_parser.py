"""
Query Parser for MCP Financial Analyzer Orchestrator

This module analyzes user queries to determine:
- Which agents need to be invoked
- The correct sequence for agent execution
- Query routing and parameter extraction
- Workflow type classification
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

from .context_manager import AgentType

logger = logging.getLogger(__name__)


class WorkflowType(Enum):
    """Types of workflows supported by the orchestrator."""
    SHORTAGE_ANALYSIS = "shortage_analysis"
    SUPPLIER_ANALYSIS = "supplier_analysis"
    CUSTOMER_ANALYSIS = "customer_analysis"
    INVENTORY_ANALYSIS = "inventory_analysis"
    PRODUCTION_ANALYSIS = "production_analysis"
    COMPREHENSIVE_ANALYSIS = "comprehensive_analysis"
    EMERGENCY_PROCUREMENT = "emergency_procurement"
    RISK_ASSESSMENT = "risk_assessment"


@dataclass
class AgentExecutionPlan:
    """Plan for executing a specific agent."""
    agent_type: AgentType
    priority: int  # Lower numbers execute first
    parameters: Dict[str, Any]
    depends_on: List[AgentType] = None  # Agent types this depends on
    optional: bool = False  # Whether this agent is optional

    def __post_init__(self):
        if self.depends_on is None:
            self.depends_on = []


@dataclass
class WorkflowPlan:
    """Complete plan for executing a workflow."""
    workflow_type: WorkflowType
    agent_plans: List[AgentExecutionPlan]
    estimated_duration_seconds: Optional[float] = None
    confidence_score: float = 0.0  # 0.0 to 1.0

    def get_execution_order(self) -> List[AgentExecutionPlan]:
        """Get agents in execution order based on dependencies and priorities."""
        # Sort by priority first, then handle dependencies
        sorted_plans = sorted(self.agent_plans, key=lambda x: x.priority)
        
        # Simple dependency resolution - in a real system you'd want topological sort
        execution_order = []
        remaining_plans = sorted_plans.copy()
        
        while remaining_plans:
            # Find plans with no unmet dependencies
            ready_plans = []
            for plan in remaining_plans:
                if not plan.depends_on:
                    ready_plans.append(plan)
                else:
                    # Check if all dependencies are already in execution order
                    deps_met = all(
                        any(ep.agent_type == dep for ep in execution_order)
                        for dep in plan.depends_on
                    )
                    if deps_met:
                        ready_plans.append(plan)
            
            if not ready_plans:
                # Circular dependency or missing dependency - add remaining by priority
                logger.warning("Circular or missing dependencies detected, adding by priority")
                ready_plans = remaining_plans
            
            # Add ready plans to execution order
            for plan in ready_plans:
                execution_order.append(plan)
                remaining_plans.remove(plan)
        
        return execution_order


class QueryParser:
    """
    Parses user queries to determine appropriate workflow and agent execution plans.
    """

    def __init__(self):
        self.workflow_patterns = self._initialize_workflow_patterns()
        self.agent_keywords = self._initialize_agent_keywords()

    def _initialize_workflow_patterns(self) -> Dict[WorkflowType, List[str]]:
        """Initialize regex patterns for different workflow types."""
        return {
            WorkflowType.SHORTAGE_ANALYSIS: [
                r'\b(shortage|shortfall|deficit|insufficient|lack|depleted)\b',
                r'\b(inventory|stock|component|material|supply)\b.*\b(low|critical|empty)\b',
                r'\b(out of stock|stockout|backorder)\b',
                r'\b(shortage index|shortage risk|supply risk)\b'
            ],
            WorkflowType.SUPPLIER_ANALYSIS: [
                r'\b(supplier|vendor|provider)\b.*\b(reliability|performance|risk)\b',
                r'\b(supplier.*payment|payment.*default|credit risk)\b',
                r'\b(supplier.*delay|delivery.*delay|lead time)\b',
                r'\b(alternative.*supplier|backup.*supplier|diversification)\b'
            ],
            WorkflowType.CUSTOMER_ANALYSIS: [
                r'\b(customer|client)\b.*\b(priority|conflict|allocation)\b',
                r'\b(customer.*credit|payment.*terms|credit risk)\b',
                r'\b(delivery.*commitment|customer.*order)\b',
                r'\b(multi.*customer|customer.*competition)\b'
            ],
            WorkflowType.INVENTORY_ANALYSIS: [
                r'\b(inventory|stock)\b.*\b(analysis|assessment|review)\b',
                r'\b(obsolescence|slow.*moving|excess.*inventory)\b',
                r'\b(inventory.*turnover|carrying.*cost)\b',
                r'\b(safety.*stock|minimum.*stock|reorder)\b'
            ],
            WorkflowType.PRODUCTION_ANALYSIS: [
                r'\b(production|manufacturing|assembly)\b.*\b(capacity|bottleneck)\b',
                r'\b(work.*order|production.*schedule|factory.*capacity)\b',
                r'\b(material.*issuance|production.*floor)\b',
                r'\b(capacity.*utilization|production.*planning)\b'
            ],
            WorkflowType.EMERGENCY_PROCUREMENT: [
                r'\b(emergency|urgent|critical|immediate)\b.*\b(procurement|purchase|sourcing)\b',
                r'\b(expedited.*delivery|rush.*order|emergency.*supplier)\b',
                r'\b(urgent.*request|immediate.*need|critical.*shortage)\b'
            ],
            WorkflowType.RISK_ASSESSMENT: [
                r'\b(risk.*assessment|vulnerability|stress.*test)\b',
                r'\b(supply.*chain.*risk|operational.*risk)\b',
                r'\b(currency.*impact|exchange.*rate|financial.*risk)\b',
                r'\b(end.*to.*end|comprehensive.*analysis|system.*stress)\b'
            ],
            WorkflowType.COMPREHENSIVE_ANALYSIS: [
                r'\b(comprehensive|complete|full|end.*to.*end)\b.*\b(analysis|assessment)\b',
                r'\b(supply.*chain.*analysis|financial.*analysis)\b',
                r'\b(overall|total|entire|whole)\b.*\b(system|operation|business)\b'
            ]
        }

    def _initialize_agent_keywords(self) -> Dict[AgentType, List[str]]:
        """Initialize keywords that suggest specific agents should be used."""
        return {
            AgentType.MYSQL: [
                'database', 'query', 'historical', 'data', 'records', 'table',
                'customer order', 'supplier', 'BOM', 'work order', 'purchase request',
                'material', 'component', 'inventory level', 'stock level'
            ],
            AgentType.SHORTAGE_ANALYZER: [
                'shortage', 'shortage index', 'risk level', 'supply risk', 'shortage analysis',
                'component shortage', 'material shortage', 'inventory shortage', 'stockout',
                'shortage calculation', 'risk assessment', 'supply chain risk'
            ],
            AgentType.ALERT_MANAGER: [
                'alert', 'notification', 'notify', 'send alert', 'warning', 'alarm',
                'email notification', 'alert management', 'notification system',
                'send notification', 'alert generation', 'notify stakeholders'
            ]
        }

    def parse_query(self, user_query: str) -> WorkflowPlan:
        """
        Parse a user query and return a workflow execution plan.
        
        Args:
            user_query: The user's input query
            
        Returns:
            WorkflowPlan with agent execution plans and workflow type
        """
        logger.info(f"Parsing query: {user_query}")
        
        # Normalize query for analysis
        normalized_query = user_query.lower().strip()
        
        # Determine workflow type
        workflow_type = self._classify_workflow_type(normalized_query)
        logger.info(f"Classified workflow type: {workflow_type}")
        
        # Determine required agents
        required_agents = self._determine_required_agents(normalized_query, workflow_type)
        logger.info(f"Required agents: {[agent.value for agent in required_agents]}")
        
        # Create agent execution plans
        agent_plans = self._create_agent_plans(normalized_query, workflow_type, required_agents)
        
        # Calculate confidence score
        confidence_score = self._calculate_confidence_score(normalized_query, workflow_type, required_agents)
        
        # Estimate duration
        estimated_duration = self._estimate_duration(agent_plans)
        
        workflow_plan = WorkflowPlan(
            workflow_type=workflow_type,
            agent_plans=agent_plans,
            estimated_duration_seconds=estimated_duration,
            confidence_score=confidence_score
        )
        
        logger.info(f"Created workflow plan with {len(agent_plans)} agents, confidence: {confidence_score:.2f}")
        return workflow_plan

    def _classify_workflow_type(self, query: str) -> WorkflowType:
        """Classify the workflow type based on query patterns."""
        scores = {}
        
        for workflow_type, patterns in self.workflow_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, query, re.IGNORECASE))
                score += matches
            scores[workflow_type] = score
        
        # Find the workflow type with the highest score
        if scores:
            best_workflow = max(scores, key=scores.get)
            if scores[best_workflow] > 0:
                return best_workflow
        
        # Default to comprehensive analysis if no specific pattern matches
        return WorkflowType.COMPREHENSIVE_ANALYSIS

    def _determine_required_agents(self, query: str, workflow_type: WorkflowType) -> List[AgentType]:
        """Determine which agents are required based on query and workflow type."""
        required_agents = []
        
        # Check for explicit agent keywords
        agent_scores = {}
        for agent_type, keywords in self.agent_keywords.items():
            score = sum(1 for keyword in keywords if keyword in query)
            agent_scores[agent_type] = score
        
        # Default agent sequences based on workflow type
        default_sequences = {
            WorkflowType.SHORTAGE_ANALYSIS: [AgentType.MYSQL, AgentType.SHORTAGE_ANALYZER, AgentType.ALERT_MANAGER],
            WorkflowType.SUPPLIER_ANALYSIS: [AgentType.MYSQL, AgentType.SHORTAGE_ANALYZER, AgentType.ALERT_MANAGER],
            WorkflowType.CUSTOMER_ANALYSIS: [AgentType.MYSQL, AgentType.SHORTAGE_ANALYZER, AgentType.ALERT_MANAGER],
            WorkflowType.INVENTORY_ANALYSIS: [AgentType.MYSQL, AgentType.SHORTAGE_ANALYZER, AgentType.ALERT_MANAGER],
            WorkflowType.PRODUCTION_ANALYSIS: [AgentType.MYSQL, AgentType.SHORTAGE_ANALYZER, AgentType.ALERT_MANAGER],
            WorkflowType.EMERGENCY_PROCUREMENT: [AgentType.MYSQL, AgentType.SHORTAGE_ANALYZER, AgentType.ALERT_MANAGER],
            WorkflowType.RISK_ASSESSMENT: [AgentType.MYSQL, AgentType.SHORTAGE_ANALYZER, AgentType.ALERT_MANAGER],
            WorkflowType.COMPREHENSIVE_ANALYSIS: [AgentType.MYSQL, AgentType.SHORTAGE_ANALYZER, AgentType.ALERT_MANAGER]
        }
        
        # Start with default sequence for workflow type
        required_agents = default_sequences.get(workflow_type, [AgentType.MYSQL, AgentType.SHORTAGE_ANALYZER, AgentType.ALERT_MANAGER])
        
        # Adjust based on explicit keywords
        if agent_scores[AgentType.MYSQL] == 0 and 'database' not in query and 'historical' not in query:
            # If no database keywords, but we still need data, keep MySQL
            pass  # Keep MySQL as it's usually needed
        
        return required_agents

    def _create_agent_plans(self, query: str, workflow_type: WorkflowType, required_agents: List[AgentType]) -> List[AgentExecutionPlan]:
        """Create detailed execution plans for each required agent."""
        plans = []
        
        for i, agent_type in enumerate(required_agents):
            if agent_type == AgentType.MYSQL:
                plans.append(AgentExecutionPlan(
                    agent_type=agent_type,
                    priority=1,
                    parameters={"query": query},
                    depends_on=[],
                    optional=False
                ))
            elif agent_type == AgentType.SHORTAGE_ANALYZER:
                plans.append(AgentExecutionPlan(
                    agent_type=agent_type,
                    priority=2,
                    parameters={"message": f"Analyze shortage for: {query}"},
                    depends_on=[AgentType.MYSQL],
                    optional=False
                ))
            elif agent_type == AgentType.ALERT_MANAGER:
                plans.append(AgentExecutionPlan(
                    agent_type=agent_type,
                    priority=3,
                    parameters={"message": f"Generate alerts for: {query}"},
                    depends_on=[AgentType.SHORTAGE_ANALYZER],
                    optional=False
                ))
        
        return plans

    def _calculate_confidence_score(self, query: str, workflow_type: WorkflowType, required_agents: List[AgentType]) -> float:
        """Calculate confidence score for the workflow plan."""
        # Base confidence on pattern matches
        patterns = self.workflow_patterns.get(workflow_type, [])
        pattern_matches = sum(1 for pattern in patterns if re.search(pattern, query, re.IGNORECASE))
        
        if not patterns:
            return 0.5  # Medium confidence for unknown patterns
        
        pattern_confidence = min(pattern_matches / len(patterns), 1.0)
        
        # Adjust based on query length and specificity
        length_factor = min(len(query.split()) / 10, 1.0)  # Longer queries tend to be more specific
        
        # Combine factors
        confidence = (pattern_confidence * 0.7) + (length_factor * 0.3)
        return min(confidence, 1.0)

    def _estimate_duration(self, agent_plans: List[AgentExecutionPlan]) -> float:
        """Estimate total execution duration in seconds."""
        # Base estimates per agent type (in seconds)
        base_durations = {
            AgentType.MYSQL: 30.0,
            AgentType.SHORTAGE_ANALYZER: 45.0,
            AgentType.ALERT_MANAGER: 20.0
        }
        
        total_duration = 0.0
        for plan in agent_plans:
            base_duration = base_durations.get(plan.agent_type, 30.0)
            # Add some overhead for coordination
            total_duration += base_duration + 5.0
        
        return total_duration
